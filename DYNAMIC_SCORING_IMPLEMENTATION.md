# Dynamic IVF Scoring System Implementation

## Overview

The IVF Fertility Meter scoring system has been successfully converted from a static implementation to a dynamic system that uses the scoring configurations defined by admins through the form builder interface.

## Key Components

### 1. Database Schema
- **`scoring_type`**: Enum field (`range` | `single_choice`) in `fertility_questions` table
- **`scoring_config`**: JSON field storing scoring rules in `fertility_questions` table  
- **`score`**: Float field in `form_options` table for single choice scoring

### 2. Core Services

#### `dynamic-scoring.service.ts`
- **`getFertilityQuestionsWithScoring()`**: Fetches all questions with scoring configurations
- **`calculateQuestionScore()`**: Calculates score for individual questions based on user answers
- **`calculateDynamicIVFScore()`**: Main function that calculates the complete IVF score
- **`mapIVFDataToAnswers()`**: Maps IVF score data to question answers

#### `scoring-preview.ts`
- **`previewRangeScoring()`**: Preview how range scoring works with sample inputs
- **`validateRangeScoring()`**: Validates range scoring configurations
- **`validateSingleChoiceScoring()`**: Validates single choice scoring configurations
- **`getScoringRecommendations()`**: Provides scoring recommendations based on question type

### 3. Updated API Routes

#### `/api/v1/ivf-scores/results`
- **GET**: Now uses `calculateDynamicIVFScore()` for authenticated users
- **GET**: Also uses dynamic scoring for verified guest sessions  
- **POST**: Uses dynamic scoring when converting guest sessions to user accounts

## Scoring Logic

### Range Scoring
```json
{
  "scoring_type": "range",
  "scoring_config": [
    { "min": 0, "max": 34, "score": 1.0 },
    { "min": 35, "max": 37, "score": 0.9 },
    { "min": 38, "max": 40, "score": 0.7 }
  ]
}
```

### Single Choice Scoring
```json
{
  "scoring_type": "single_choice",
  "options": [
    { "option_text": "Yes", "score": 1 },
    { "option_text": "No", "score": 0 }
  ]
}
```

## IVF Fertility Meter Formula

The system implements the exact formula from the instruction document:

```
IVF-SPSNew = 100 × (0.5 × Pnew + 0.25 × Psynew + 0.25 × Enew)
```

Where:
- **Pnew**: Normalized biological/physiological score (0-1)
- **Psynew**: Normalized lifestyle/psychosocial score (0-1)  
- **Enew**: Normalized environmental/socioeconomic score (0-1)

**Minimum Score**: 60 (as specified in the instruction document)

## Category Mapping

Forms are automatically categorized based on their names:
- **Biological**: Forms containing "biological" or "physiological"
- **Lifestyle**: Forms containing "lifestyle" or "psychosocial"
- **Environmental**: Forms containing "environmental" or "socioeconomic"

## Question-to-Data Mapping

The system maps question text to IVF data fields:

| Question Pattern | Data Field |
|-----------------|------------|
| "age" | `age` |
| "height" | `height` |
| "weight" | `weight` |
| "bmi" | `bmi` (calculated) |
| "menstrual" | `menstrual_regularity` |
| "infertility duration" | `infertility_duration` |
| "ivf attempt" | `ivf_attempts` |
| "stress level" | `stress_level` |
| "diet" | `diet_type` |
| "exercise" | `exercise_frequency` |
| "sleep" | `sleep_quality` |
| "emotional support" | `emotional_support_at_home` |
| "income" | `household_income_range` |
| "living area" | `living_area` |
| "work stress" | `work_stress_level` |
| "pollution" | `pollution_exposure` |
| "occupation" | `occupation_type` |

## Error Handling

The system includes robust error handling:
- **Graceful Fallbacks**: Returns minimum score (60) if dynamic calculation fails
- **Validation**: Validates scoring configurations before use
- **Type Safety**: Full TypeScript support with proper type checking
- **Logging**: Comprehensive error logging for debugging

## Testing

### Unit Tests (`dynamic-scoring.test.ts`)
- Range scoring validation
- Single choice scoring validation  
- Data mapping verification
- Error handling verification
- Integration testing

### Preview Utilities (`scoring-preview.ts`)
- Configuration validation
- Sample input generation
- Scoring recommendations
- Real-time preview functionality

## Admin Experience

### Question Builder Integration
1. **Scoring Type Selection**: Choose between range and single choice
2. **Configuration Interface**: 
   - Range scoring: Define min/max/score triplets
   - Single choice: Assign scores to each option
3. **Validation**: Real-time validation with error messages
4. **Preview**: See how scoring will work with sample inputs

### Validation Features
- **Range Overlap Detection**: Prevents overlapping ranges
- **Gap Detection**: Warns about gaps in range coverage
- **Score Validation**: Ensures non-negative scores
- **Duplicate Detection**: Prevents duplicate options

## Migration from Static to Dynamic

### What Changed
1. **Removed**: Static `calculateIVFScore()` function
2. **Added**: Dynamic scoring service with database-driven configuration
3. **Enhanced**: API routes to use dynamic scoring
4. **Improved**: Error handling and fallback mechanisms

### Backward Compatibility
- Existing IVF score data remains unchanged
- Questions without scoring configuration are ignored (score 0)
- System gracefully handles missing or invalid configurations

## Performance Considerations

- **Database Queries**: Optimized to fetch only questions with scoring configurations
- **Caching**: Consider implementing caching for frequently accessed scoring configurations
- **Validation**: Client-side validation reduces server load
- **Fallbacks**: Fast fallback to minimum score prevents timeouts

## Future Enhancements

1. **Advanced Mapping**: More sophisticated question-to-data field mapping
2. **Weighted Categories**: Allow admins to configure category weights
3. **Score History**: Track how scoring configurations change over time
4. **A/B Testing**: Test different scoring configurations
5. **Analytics**: Track which questions contribute most to final scores

## Usage Examples

### Creating a Range-Scored Question
```typescript
const ageQuestion = {
  question_text: "What is your age?",
  field_type: FormFieldType.NUMBER_INPUT,
  scoring_type: ScoringType.range,
  scoring_config: [
    { min: 0, max: 34, score: 1.0 },
    { min: 35, max: 37, score: 0.9 },
    { min: 38, max: 40, score: 0.7 }
  ]
};
```

### Creating a Single Choice Question
```typescript
const supportQuestion = {
  question_text: "Do you have emotional support at home?",
  field_type: FormFieldType.RADIO_SELECT,
  scoring_type: ScoringType.single_choice,
  options: [
    { option_text: "Yes", score: 1 },
    { option_text: "No", score: 0 }
  ]
};
```

## Conclusion

The dynamic scoring system provides a flexible, maintainable, and user-friendly way for admins to configure how fertility meter scores are calculated. It follows the exact specifications from the instruction document while providing robust error handling and validation.

The system is now ready for production use and can be easily extended with additional features as needed.
