# IVF Fertility Questions

This document explains the business logic and implementation of the IVF fertility questions and the dynamic scoring system.

## Overview

The IVF fertility meter is a key feature of the GIVF application. It uses a series of questions to assess a user's fertility and calculate a score. The questions and their scoring logic are managed dynamically by administrators through the admin dashboard.

## Key Files

-   **`IVF_Fertility_Meter_SCORING_SYSTEM.md`**: The main documentation for the dynamic scoring system.
-   **`prisma/schema/fertility_questions.prisma`**: The Prisma schema for the fertility questions and their scoring configurations.
-   **`src/app/admin/fertility-meter-questions/`**: The admin page for managing the fertility questions.
-   **`src/lib/services/dynamic-scoring.service.ts`**: The core service that calculates the IVF score based on user answers and the dynamic scoring configuration.
-   **`src/tests/dynamic-scoring.test.ts`**: Unit tests for the dynamic scoring service.

## Data Model

The fertility questions are stored in the database. The key fields in the `fertility_questions` table are:
-   `question_text`: The text of the question.
-   `field_type`: The type of input field (e.g., `NUMBER_INPUT`, `RADIO_SELECT`).
-   `scoring_type`: The type of scoring logic to apply, which can be `range` or `single_choice`.
-   `scoring_config`: A JSON field that stores the scoring rules.

### Range Scoring

For questions with `scoring_type = 'range'`, the `scoring_config` contains an array of objects, each with a `min`, `max`, and `score`.
Example for an "age" question:
```json
[
  { "min": 0, "max": 34, "score": 1.0 },
  { "min": 35, "max": 37, "score": 0.9 },
  { "min": 38, "max": 40, "score": 0.7 }
]
```

### Single Choice Scoring

For questions with `scoring_type = 'single_choice'`, the scoring is defined in the `options` related to the question. Each option has a `score` associated with it.
Example for a "Do you smoke?" question:
-   Option "Yes": `score: 0`
-   Option "No": `score: 1`

## Dynamic Scoring Service

The `dynamic-scoring.service.ts` contains the logic for calculating the score.
-   `calculateQuestionScore()`: This function takes a question and a user's answer, and returns the score for that single question based on its `scoring_type` and `scoring_config`.
-   `calculateDynamicIVFScore()`: This is the main function that orchestrates the entire scoring process. It fetches all fertility questions with scoring, calculates the score for each based on the user's answers, and then applies the final IVF formula.

## IVF Score Formula

The final IVF score is calculated using the formula specified in the requirements:
`IVF-SPSNew = 100 × (0.5 × Pnew + 0.25 × Psynew + 0.25 × Enew)`

The service categorizes questions into "Biological", "Lifestyle", and "Environmental" based on their form name to calculate the `Pnew`, `Psynew`, and `Enew` components of the formula.

## Admin Management

Administrators can manage the entire question and scoring system through the "Fertility Questions" page in the admin dashboard. This allows for a flexible system that can be updated without any code changes, directly by the system administrators.
