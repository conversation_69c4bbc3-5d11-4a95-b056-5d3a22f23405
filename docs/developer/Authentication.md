# Authentication

This document describes the authentication system in the GIVF application, which is built using Supabase Auth.

## Overview

Authentication is managed through a combination of Supabase's server-side and client-side libraries. The `useAuth` hook provides a simple way to access the current user's authentication state throughout the application.

## Key Files

-   **`src/hooks/useAuth.ts`**: A custom React hook that provides the current user and loading state. It wraps Supabase's `onAuthStateChange` to ensure the user state is always up-to-date.
-   **`src/utils/supabase/client.ts`**: Initializes the Supabase client for client-side use.
-   **`src/utils/supabase/server.ts`**: Initializes the Supabase client for server-side use (e.g., in API routes).
-   **`src/app/login/page.tsx`**: The login page component.
-   **`src/app/signup/page.tsx`**: The signup page component.
-   **`middleware.ts`**: The middleware is responsible for handling session management and protecting routes.

## `useAuth` Hook

The `useAuth` hook is the primary way to interact with the authentication state in components.

```typescript
import { useAuth } from '@/hooks/useAuth';

function UserProfile() {
  const { user, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <div>Please log in.</div>;
  }

  return <div>Welcome, {user.email}</div>;
}
```

## Authentication Flow

1.  **User signs up or logs in:** The user provides their credentials on the login or signup page.
2.  **Supabase handles authentication:** The Supabase client communicates with the Supabase backend to authenticate the user.
3.  **Session is created:** Upon successful authentication, Supabase creates a session and stores it in the browser's cookies.
4.  **`onAuthStateChange` is triggered:** The `useAuth` hook's listener fires, updating the application's state with the new user information.
5.  **Authenticated requests:** The Supabase client automatically includes the user's JWT in the headers of subsequent requests to the backend and the database.

## Protected Routes

Route protection is handled by the `middleware.ts` file and the `AdminAuthWrapper.tsx` component.

-   **`middleware.ts`**: This runs on the server before a request is completed. It checks for a valid session and redirects unauthenticated users from protected pages to the login page.
-   **`AdminAuthWrapper.tsx`**: This component is used to protect the admin dashboard. It uses the `useAdminAuth` hook, which in turn uses the `useUserCan` hook to check if the user has the `admin.access` permission.

## GraphQL and API Routes

For API routes, especially the GraphQL endpoint (`/api/graphql`), the access token is parsed from the request headers and verified using `verifyAccessToken`. This ensures that only authenticated users can access the GraphQL API.
