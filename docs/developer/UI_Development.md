# UI Development

This document covers the tools and conventions used for UI development in the GIVF project.

## Overview

The UI is built with Next.js and React, styled with Tailwind CSS, and uses Shadcn UI for its component library. Storybook is used for component development and testing in isolation.

## Key Files and Directories

-   **`src/app/`**: The main directory for pages and layouts.
-   **`src/components/`**: Contains all reusable React components.
    -   **`ShadcnUI/`**: The core UI components from the Shadcn library. These are the building blocks for the application's UI.
    -   **`admin/`**, **`shared/`**, etc.: Application-specific components.
-   **`src/styles/`**: Global and utility CSS files.
-   **`tailwind.config.mjs`**: The configuration file for Tailwind CSS.
-   **`.storybook/`**: Configuration files for Storybook.
-   **`components.json`**: Configuration for the Shadcn UI components.

## Component Library: Shadcn UI

Shadcn UI is used as the primary component library. It provides a set of accessible and customizable components that are used to build the user interface. The components are located in `src/components/ShadcnUI/`.

When you need a new UI element, first check if it can be built using the existing Shadcn components. If not, you can create a new custom component.

## Styling: Tailwind CSS

Styling is done primarily with Tailwind CSS, a utility-first CSS framework. This allows for rapid development and a consistent design system. Custom styles and overrides are kept in the `src/styles/` directory.

-   **`globals.css`**: Global styles for the application.
-   **`admin-theme.css`**: Specific theme for the admin dashboard.

## Storybook

Storybook is used for developing and documenting UI components in isolation. Each component should have a corresponding `.stories.tsx` file.

To run Storybook:
```bash
npm run storybook
```
This will start the Storybook development server, where you can view and interact with the components.

### Creating Stories

When you create a new component, you should also create a story for it. This helps with testing and provides a living documentation of the component's different states and variations.

Example of a story file (`Button.stories.tsx`):
```typescript
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Primary: Story = {
  args: {
    children: 'Primary Button',
    variant: 'primary',
  },
};
```

This approach ensures that components are developed in a consistent and maintainable way.
