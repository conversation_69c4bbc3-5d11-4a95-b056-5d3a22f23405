# API

The GIVF application exposes both RESTful and GraphQL APIs to the client. This document details their structure and usage.

## Overview

The API is built using Next.js API Routes. The primary API endpoint is for GraphQL, but there are also RESTful endpoints for specific functionalities.

## Key Files

-   **`src/app/api/graphql/route.ts`**: The main entry point for the GraphQL API. It proxies requests to the Supabase GraphQL endpoint.
-   **`src/app/api/v1/`**: Directory for version 1 of the RESTful API.
-   **`docs/developer/GRAPHQL.md`**: Documentation for the GraphQL schema and queries.

## GraphQL API

The application uses the Supabase GraphQL endpoint, which provides a GraphQL interface to the PostgreSQL database. The Next.js API route at `src/app/api/graphql/route.ts` acts as a proxy, forwarding requests to the Supabase GraphQL URL.

### Authentication

The GraphQL proxy (`/api/graphql/route.ts`) enforces authentication. It extracts the JWT from the `Authorization` header of incoming requests and verifies it using the `verifyAccessToken` utility. If the token is valid, the request is forwarded to the Supabase GraphQL endpoint with the user's token, ensuring that all database requests are made on behalf of the authenticated user and are subject to Row-Level Security policies.

### Usage

Clients can send GraphQL queries and mutations to the `/api/graphql` endpoint. The queries should conform to the schema defined by the database tables.

## REST API

For functionalities that don't fit the GraphQL model or require a simpler interface, the application uses RESTful API endpoints located in `src/app/api/v1/`.

### Example: Roles API

An example of a RESTful endpoint is the Roles API, which is used for managing roles and permissions in the admin dashboard. The API routes for this would be located in a file like `src/app/api/v1/admin/roles/route.ts`.

These endpoints would handle standard HTTP methods:
-   `GET /api/v1/admin/roles`: Fetches all roles.
-   `POST /api/v1/admin/roles`: Creates a new role.
-   `PUT /api/v1/admin/roles`: Updates an existing role.
-   `DELETE /api/v1/admin/roles`: Deletes a role.

Like the GraphQL endpoint, these RESTful endpoints also enforce authentication and permissions, ensuring that only authorized users can perform administrative actions.
