# Scoring System

This document details the technical implementation of the dynamic scoring system for the IVF Fertility Meter.

## Overview

The scoring system is designed to be dynamic, allowing administrators to configure how scores are calculated without changing the application's code. The logic is primarily handled by a dedicated service that interprets scoring rules stored in the database.

## Key Files

-   **`IVF_Fertility_Meter_SCORING_SYSTEM.md`**: The high-level documentation for the scoring system.
-   **`src/lib/services/dynamic-scoring.service.ts`**: The core service responsible for all score calculations.
-   **`src/tests/dynamic-scoring.test.ts`**: Unit tests that verify the correctness of the scoring logic.
-   **`src/types/fertility-questions.ts`**: TypeScript types related to fertility questions and scoring configurations.
-   **`prisma/schema/fertility_questions.prisma`**: The database schema for storing questions and their scoring rules.

## Core Logic: `dynamic-scoring.service.ts`

This service contains the key functions that power the scoring system.

### `calculateQuestionScore(question, answer)`

This function is the heart of the scoring logic for individual questions. It determines the question's `scoring_type` and applies the corresponding logic:

-   **For `range` scoring:** It parses the `scoring_config` JSON and finds which range the `answer` falls into, then returns the associated `score`.
-   **For `single_choice` scoring:** It finds the option that matches the `answer` and returns the `score` stored on that option.
-   It includes error handling to gracefully manage invalid configurations or answers, typically returning a score of `0`.

### `mapIVFDataToAnswers(ivfData)`

This utility function maps the raw data from an `ivf_scores` table row to a more usable format of question-answer pairs. It also performs calculations for derived values, such as calculating the `bmi` from `height` and `weight`.

### `calculateDynamicIVFScore(ivfData)`

This is the main orchestrator function. It performs the following steps:
1.  Fetches all fertility questions that have a scoring configuration from the database.
2.  Uses `mapIVFDataToAnswers` to get the user's answers.
3.  Iterates through each question, calling `calculateQuestionScore` for each one to get an individual score.
4.  Categorizes the scores into Biological, Lifestyle, and Environmental based on the form name associated with each question.
5.  Normalizes the scores for each category.
6.  Applies the final IVF formula: `100 × (0.5 × Pnew + 0.25 × Psynew + 0.25 × Enew)`.
7.  Includes a fallback mechanism to return a minimum score if any part of the calculation fails, ensuring the system is resilient.

## Testing

The `src/tests/dynamic-scoring.test.ts` file contains a suite of unit tests to ensure the scoring system is working correctly. These tests cover:
-   Correct score calculation for range-based questions.
-   Correct score calculation for single-choice questions.
-   Accurate data mapping and BMI calculation.
-   Graceful error handling for invalid configurations or inputs.

This test suite is crucial for maintaining the integrity of the scoring system as the application evolves.
