# Tracks System Implementation

## Overview

A comprehensive tracks system has been implemented for the fertility meter questions, allowing admins to categorize questions into three tracks (T1, T2, T3) and enabling users to filter questions based on their fertility journey stage.

## Database Schema Changes

### New Tables

#### `tracks` Table
```sql
- id: UUID (Primary Key)
- code: String (Unique) - T1, T2, T3
- name: String - Track 1, Track 2, Track 3
- description: String - Detailed description
- order: Int - Display order
- created_at: DateTime
- updated_at: DateTime
```

#### `question_tracks` Junction Table
```sql
- id: UUID (Primary Key)
- question_id: UUID (Foreign Key to fertility_questions)
- track_id: UUID (Foreign Key to tracks)
- created_at: DateTime
- Unique constraint on (question_id, track_id)
```

### Updated Tables

#### `fertility_questions` Table
- Added relation: `question_tracks[]` - Many-to-many relationship with tracks

## Track Definitions

| Code | Name | Description |
|------|------|-------------|
| T1 | Track 1 | I have not gotten any testing done |
| T2 | Track 2 | I have gotten fertility tests done |
| T3 | Track 3 | I have done IVF before |

## Core Services

### `tracks.service.ts`
- **`getAllTracks()`**: Fetch all available tracks
- **`getQuestionTracks(questionId)`**: Get tracks assigned to a specific question
- **`assignTracksToQuestion(questionId, trackIds)`**: Assign multiple tracks to a question
- **`getQuestionsByTrack(trackId)`**: Get questions filtered by a single track
- **`getQuestionsByTracks(trackIds)`**: Get questions filtered by multiple tracks (OR condition)
- **`getTrackStatistics()`**: Get track usage statistics
- **`isQuestionInTrack(questionId, trackId)`**: Check if question is in a specific track

### Updated `fertility-questions.service.ts`
- **`transformQuestionWithTracks()`**: Transform database results to include track IDs
- **`addQuestion()`**: Now handles track assignment during question creation
- **`updateQuestion()`**: Now handles track updates during question modification
- All fetch functions now include track information in results

## Admin Interface Components

### `TrackSelector.tsx`
- **Purpose**: Allow admins to assign tracks to questions during creation/editing
- **Features**:
  - Checkbox interface for multiple track selection
  - Real-time validation and feedback
  - Visual indicators for selected tracks
  - Loading states and error handling
  - Track descriptions and codes display

### Updated `QuestionBuilder.tsx`
- **New Features**:
  - Integrated TrackSelector component
  - Track assignment handling in form data
  - Track validation and persistence
  - Preview includes track information

## Frontend Components

### `TrackFilter.tsx`
- **Purpose**: Allow users to filter questions by track on the frontend
- **Two Variants**:
  1. **Full TrackFilter**: Complete card-based interface with descriptions
  2. **CompactTrackFilter**: Inline button-based interface for space-constrained areas

- **Features**:
  - Radio button selection for single track filtering
  - "All Questions" option to show unfiltered results
  - Track descriptions and visual indicators
  - Loading states and error handling
  - Responsive design

## Usage Examples

### Admin: Creating a Question with Tracks
```typescript
const questionData = {
  question_text: "Have you had fertility testing done?",
  field_type: FormFieldType.RADIO_SELECT,
  tracks: ["track-t1-id", "track-t2-id"], // Assign to T1 and T2
  options: [
    { option_text: "Yes", score: 1 },
    { option_text: "No", score: 0 }
  ]
};
```

### Frontend: Filtering Questions by Track
```typescript
// Get questions for T1 track only
const t1Questions = await getQuestionsByTrack("track-t1-id");

// Get questions for multiple tracks
const multiTrackQuestions = await getQuestionsByTracks(["track-t1-id", "track-t2-id"]);
```

### Component Usage
```jsx
// Full track filter
<TrackFilter
  selectedTrack={selectedTrackId}
  onTrackSelect={setSelectedTrackId}
  showAllOption={true}
/>

// Compact track filter
<CompactTrackFilter
  selectedTrack={selectedTrackId}
  onTrackSelect={setSelectedTrackId}
/>
```

## Data Seeding

### Initial Track Data
The system includes a seed script (`prisma/seeds/tracks.ts`) that populates the tracks table with the three predefined tracks:

```typescript
const tracks = [
  { code: "T1", name: "Track 1", description: "I have not gotten any testing done", order: 1 },
  { code: "T2", name: "Track 2", description: "I have gotten fertility tests done", order: 2 },
  { code: "T3", name: "Track 3", description: "I have done IVF before", order: 3 }
];
```

## API Integration

### Question Filtering
Questions can now be filtered by track in API responses:
- Questions include track information in their metadata
- Frontend can request questions filtered by specific tracks
- Dynamic scoring system respects track filtering

### Admin Operations
- Track assignment is handled automatically during question CRUD operations
- Bulk track operations are supported through transaction-based updates
- Track statistics are available for admin dashboards

## User Experience Flow

### For Users
1. **Track Selection**: Users select their track (T1, T2, or T3) based on their fertility journey
2. **Filtered Questions**: Only questions relevant to their selected track are displayed
3. **Personalized Experience**: Questions are tailored to their specific situation

### For Admins
1. **Question Creation**: Admins can assign one or more tracks to each question
2. **Track Management**: Visual interface shows which tracks are assigned
3. **Validation**: System ensures proper track assignment and prevents orphaned questions
4. **Statistics**: Track usage statistics help optimize question distribution

## Benefits

### For Users
- **Relevant Content**: Only see questions applicable to their situation
- **Reduced Complexity**: Fewer, more targeted questions
- **Better Experience**: Personalized fertility assessment journey

### For Admins
- **Content Organization**: Logical grouping of questions by user journey stage
- **Flexible Assignment**: Questions can belong to multiple tracks
- **Easy Management**: Visual interface for track assignment and management
- **Analytics**: Track usage statistics for optimization

## Future Enhancements

1. **Dynamic Track Creation**: Allow admins to create custom tracks
2. **Track Dependencies**: Implement track progression logic
3. **Advanced Filtering**: Combine track filtering with other criteria
4. **Track Analytics**: Detailed analytics on track usage and effectiveness
5. **User Track History**: Track user's journey progression over time

## Migration and Deployment

1. **Database Migration**: Applied via `npx prisma migrate dev --name add_tracks_system`
2. **Data Seeding**: Run `npx tsx prisma/seeds/tracks.ts` to populate initial tracks
3. **Backward Compatibility**: Existing questions without tracks continue to work
4. **Gradual Rollout**: Tracks can be assigned to existing questions incrementally

## Testing

The system includes comprehensive error handling and validation:
- Track assignment validation
- Database constraint enforcement
- Frontend error states and loading indicators
- Graceful fallbacks for missing track data

The tracks system is now fully implemented and ready for production use!
