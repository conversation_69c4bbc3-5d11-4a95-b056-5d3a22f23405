import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

export async function seedTracks() {
  console.log("Seeding tracks...");

  const tracks = [
    {
      code: "T1",
      name: "Track 1",
      description: "I have not gotten any testing done",
      order: 1,
    },
    {
      code: "T2", 
      name: "Track 2",
      description: "I have gotten fertility tests done",
      order: 2,
    },
    {
      code: "T3",
      name: "Track 3", 
      description: "I have done IVF before",
      order: 3,
    },
  ];

  for (const track of tracks) {
    await prisma.tracks.upsert({
      where: { code: track.code },
      update: {
        name: track.name,
        description: track.description,
        order: track.order,
      },
      create: track,
    });
  }

  console.log("Tracks seeded successfully!");
}

// Run the seed if this file is executed directly
if (require.main === module) {
  seedTracks()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
