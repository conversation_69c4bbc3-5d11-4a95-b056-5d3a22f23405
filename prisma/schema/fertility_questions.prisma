/// Enum to define the type of form field.
enum FormFieldType {
  INPUT // For general text input (e.g., Known Condition as free text if not dropdown)
  NUMBER_INPUT // For numerical inputs (e.g., Age, Height, Weight, Number of IVF Attempts)
  RADIO_SELECT // For radio button selections (e.g., Menstrual Regularity, Infertility Duration)
  DROPDOWN_SELECT // For dropdown selections (e.g., Known Condition as a predefined list)
  RANGE_SLIDER // For a slider to select a value within a range
}

/// Enum to define the scoring type for IVF Fertility Meter
enum ScoringType {
  range // Numeric input mapped to score buckets
  single_choice // Predefined options, each mapped to a score
}

/// Represents a single form that an admin can create.
/// For this request, it will hold "fertility meter questions".
model forms{ // Renamed model to 'forms'
  id          String             @id @default(uuid()) @db.Uuid
  name        String             @unique // e.g., "fertility meter questions"
  description String?            // Optional description for the form
  created_at  DateTime           @default(now()) @map("created_at")
  updated_at  DateTime           @updatedAt @map("updated_at")
  questions   fertility_questions[] // Relation to the questions belonging to this form
}

/// Represents a single question or field within a form.
/// This table is designed to be flexible enough to handle all specified field types.
model fertility_questions { // Renamed model to 'fertility_questions'
  id             String      @id @default(uuid()) @db.Uuid
  form_id        String      @db.Uuid @map("form_id")
  form           forms       @relation(fields: [form_id], references: [id], onDelete: Cascade) // Links to the forms table
  question_text  String      @map("question_text") // The text of the question (e.g., "Age", "Menstrual Regularity")
  field_type     FormFieldType   @map("field_type") // The type of input field (e.g., NUMBER_INPUT, RADIO_SELECT)
  placeholder    String?     // Placeholder text for INPUT and NUMBER_INPUT fields
  min_value      Int?        @map("min_value") // Minimum value for NUMBER_INPUT and RANGE_SLIDER
  max_value      Int?        @map("max_value") // Maximum value for NUMBER_INPUT and RANGE_SLIDER
  step           Int?        // Step value for NUMBER_INPUT and RANGE_SLIDER
  unit           String?     // Unit for the input (e.g., "Yrs", "cm", "kg")
  order          Int         // To define the display order of questions within a form

  // Scoring system fields for IVF Fertility Meter
  scoring_type   ScoringType? @map("scoring_type") // Type of scoring: range or single_choice
  scoring_config Json?       @map("scoring_config") // JSON configuration for scoring rules

  created_at     DateTime    @default(now()) @map("created_at")
  updated_at     DateTime    @updatedAt @map("updated_at")
  options        form_options[]    // Relation to options for RADIO_SELECT and DROPDOWN_SELECT

  @@index([form_id]) // Creates an index on form_id for faster lookups
  // @@map("fertility_questions") // Removed @@map as model name is now 'fertility_questions'
}

/// Represents an option for RADIO_SELECT or DROPDOWN_SELECT field types.
/// For example, for "Menstrual Regularity", options would be "Regular" and "Irregular".
model form_options {
  id           String            @id @default(uuid()) @db.Uuid
  question_id  String            @db.Uuid @map("question_id")
  question     fertility_questions @relation(fields: [question_id], references: [id], onDelete: Cascade) // Links to the fertility_questions table
  option_text  String            @map("option_text") // The display text for the option (e.g., "Regular", "<6 months")
  value        String?           // The actual value to be stored if different from optionText (optional)
  order        Int               // To define the display order of options for a question

  // Scoring field for single_choice scoring type
  score        Float?            // Score value for this option when using single_choice scoring

  created_at   DateTime          @default(now()) @map("created_at")
  updated_at   DateTime          @updatedAt @map("updated_at")

  @@index([question_id]) // Creates an index on question_id for faster lookups
}
