/* ====================
   ADMIN PANEL CUSTOM THEME
   ==================== */

/* Admin Color Variables */
:root {
  /* Admin Brand Colors */
  --admin-primary: #1a202c;
  --admin-primary-light: #2d3748;
  --admin-primary-dark: #0f1419;
  
  /* Admin Accent Colors */
  --admin-accent: #d93968;
  --admin-accent-light: #fc6492;
  --admin-accent-dark: #ad1844;
  --admin-accent-hover: #de3266;
  
  /* Admin Secondary Colors */
  --admin-secondary: #d9399e;
  --admin-secondary-light: #fd74cb;
  --admin-secondary-dark: #c8248c;
  
  /* Admin Success Colors */
  --admin-success: #10b981;
  --admin-success-light: #34d399;
  --admin-success-dark: #059669;
  --admin-success-bg: #d1fae5;
  
  /* Admin Warning Colors */
  --admin-warning: #f59e0b;
  --admin-warning-light: #fbbf24;
  --admin-warning-dark: #d97706;
  --admin-warning-bg: #fef3c7;
  
  /* Admin Error Colors */
  --admin-error: #ef4444;
  --admin-error-light: #f87171;
  --admin-error-dark: #dc2626;
  --admin-error-bg: #fecaca;
  
  /* Admin Background Colors */
  --admin-bg-primary: #f8fafc;
  --admin-bg-secondary: #ffffff;
  --admin-bg-tertiary: #f1f5f9;
  --admin-bg-dark: #0f172a;
  --admin-bg-sidebar: #1e293b;
  --admin-bg-header: #ffffff;
  
  /* Admin Text Colors */
  --admin-text-primary: #1e293b;
  --admin-text-secondary: #64748b;
  --admin-text-muted: #94a3b8;
  --admin-text-white: #ffffff;
  --admin-text-light: #f8fafc;
  
  /* Admin Border Colors */
  --admin-border-light: #e2e8f0;
  --admin-border-medium: #cbd5e1;
  --admin-border-dark: #475569;
  
  /* Admin Shadow Colors */
  --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Admin Spacing */
  --admin-spacing-xs: 0.25rem;
  --admin-spacing-sm: 0.5rem;
  --admin-spacing-md: 1rem;
  --admin-spacing-lg: 1.5rem;
  --admin-spacing-xl: 2rem;
  --admin-spacing-2xl: 3rem;
  
  /* Admin Border Radius */
  --admin-radius-sm: 0.375rem;
  --admin-radius-md: 0.5rem;
  --admin-radius-lg: 0.75rem;
  --admin-radius-xl: 1rem;
  
  /* Admin Typography */
  --admin-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --admin-font-mono: 'Fira Code', 'Monaco', 'Cascadia Code', 'Segoe UI Mono', monospace;
}

/* ====================
   ADMIN LAYOUT STYLES
   ==================== */

/* Admin Body Override */
.admin-layout {
  font-family: var(--admin-font-family);
  background-color: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Admin Header Styling */
.admin-header {
  background: var(--admin-bg-header);
  border-bottom: 1px solid var(--admin-border-light);
  box-shadow: var(--admin-shadow-sm);
  backdrop-filter: blur(20px);
  background-color: rgba(255, 255, 255, 0.95);
}

.admin-header-content {
  padding: var(--admin-spacing-lg) var(--admin-spacing-xl);
}

.admin-logo {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--admin-primary);
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
  transition: color 0.2s ease;
}

.admin-logo:hover {
  color: var(--admin-accent);
}

.admin-breadcrumb {
  color: var(--admin-text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Admin Sidebar Styling */
.admin-sidebar {
  background: var(--admin-bg-sidebar);
  border-right: 1px solid var(--admin-border-medium);
  box-shadow: var(--admin-shadow-lg);
}

.admin-sidebar-content {
  padding: var(--admin-spacing-xl);
}

/* Admin Navigation Items */
.admin-nav-item {
  display: flex;
  align-items: flex-start;
  gap: var(--admin-spacing-md);
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  border-radius: var(--admin-radius-lg);
  text-decoration: none;
  color: var(--admin-text-light);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.admin-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--admin-accent), var(--admin-secondary));
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: -1;
}

.admin-nav-item:hover {
  color: var(--admin-text-white);
  transform: translateY(-1px);
  box-shadow: var(--admin-shadow-md);
}

.admin-nav-item:hover::before {
  opacity: 1;
}

.admin-nav-item.active {
  background: linear-gradient(135deg, var(--admin-accent), var(--admin-secondary));
  color: var(--admin-text-white);
  box-shadow: var(--admin-shadow-md);
}

.admin-nav-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.125rem;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.admin-nav-item:hover .admin-nav-icon {
  transform: scale(1.1);
}

.admin-nav-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: var(--admin-spacing-xs);
}

.admin-nav-description {
  font-size: 0.75rem;
  opacity: 0.8;
  line-height: 1.4;
}

/* Admin Main Content */
.admin-main-content {
  margin-left: 16rem;
  padding-top: 4rem;
  min-height: 100vh;
}

.admin-page-content {
  padding: var(--admin-spacing-2xl);
  max-width: 1400px;
  margin: 0 auto;
}

/* ====================
   ADMIN COMPONENTS
   ==================== */

/* Admin Buttons */
.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-spacing-sm);
  padding: var(--admin-spacing-sm) var(--admin-spacing-lg);
  border-radius: var(--admin-radius-md);
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.admin-btn-primary {
  background: linear-gradient(135deg, var(--admin-accent), var(--admin-secondary));
  color: var(--admin-text-white);
  box-shadow: var(--admin-shadow-sm);
}

.admin-btn-primary:hover {
  box-shadow: var(--admin-shadow-md);
  transform: translateY(-1px);
}

.admin-btn-secondary {
  background: var(--admin-bg-secondary);
  color: var(--admin-text-primary);
  border: 1px solid var(--admin-border-light);
}

.admin-btn-secondary:hover {
  background: var(--admin-bg-tertiary);
  border-color: var(--admin-border-medium);
}

.admin-btn-outline {
  background: transparent;
  color: var(--admin-text-secondary);
  border: 1px solid var(--admin-border-medium);
}

.admin-btn-outline:hover {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-primary);
}

/* Admin Cards */
.admin-card {
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-sm);
  transition: all 0.2s ease;
}

.admin-card:hover {
  box-shadow: var(--admin-shadow-md);
  border-color: var(--admin-border-medium);
}

.admin-card-header {
  padding: var(--admin-spacing-xl);
  border-bottom: 1px solid var(--admin-border-light);
}

.admin-card-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--admin-text-primary);
  margin: 0;
}

.admin-card-description {
  color: var(--admin-text-secondary);
  margin-top: var(--admin-spacing-xs);
  font-size: 0.875rem;
}

.admin-card-content {
  padding: var(--admin-spacing-xl);
}

/* Admin Tables */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-radius-lg);
  overflow: hidden;
  box-shadow: var(--admin-shadow-sm);
}

.admin-table th {
  background: var(--admin-bg-tertiary);
  padding: var(--admin-spacing-lg);
  text-align: left;
  font-weight: 600;
  color: var(--admin-text-primary);
  font-size: 0.875rem;
  border-bottom: 1px solid var(--admin-border-light);
}

.admin-table td {
  padding: var(--admin-spacing-lg);
  border-bottom: 1px solid var(--admin-border-light);
  color: var(--admin-text-secondary);
  font-size: 0.875rem;
}

.admin-table tbody tr:hover {
  background: var(--admin-bg-tertiary);
}

/* Admin Form Elements */
.admin-input {
  width: 100%;
  padding: var(--admin-spacing-md);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  background: var(--admin-bg-secondary);
  color: var(--admin-text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.admin-input:focus {
  outline: none;
  border-color: var(--admin-accent);
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.admin-label {
  display: block;
  font-weight: 600;
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-spacing-sm);
  font-size: 0.875rem;
}

/* Admin Status Badges */
.admin-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-spacing-xs) var(--admin-spacing-sm);
  border-radius: var(--admin-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.admin-badge-success {
  background: var(--admin-success-bg);
  color: var(--admin-success-dark);
}

.admin-badge-warning {
  background: var(--admin-warning-bg);
  color: var(--admin-warning-dark);
}

.admin-badge-error {
  background: var(--admin-error-bg);
  color: var(--admin-error-dark);
}

/* ====================
   ADMIN RESPONSIVE
   ==================== */

@media (max-width: 1024px) {
  .admin-main-content {
    margin-left: 0;
    padding-top: 4rem;
  }
  
  .admin-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .admin-sidebar.open {
    transform: translateX(0);
  }
  
  .admin-page-content {
    padding: var(--admin-spacing-lg);
  }
}

@media (max-width: 768px) {
  .admin-header-content {
    padding: var(--admin-spacing-md);
  }
  
  .admin-page-content {
    padding: var(--admin-spacing-md);
  }
  
  .admin-nav-item {
    padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  }
}

/* ====================
   ADMIN ANIMATIONS
   ==================== */

@keyframes admin-fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes admin-slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.admin-animate-in {
  animation: admin-fadeIn 0.3s ease-out;
}

.admin-slide-in {
  animation: admin-slideIn 0.3s ease-out;
}

/* ====================
   ADMIN UTILITIES
   ==================== */

.admin-text-gradient {
  background: linear-gradient(135deg, var(--admin-accent), var(--admin-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admin-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.admin-glow {
  box-shadow: 0 0 20px rgba(66, 153, 225, 0.3);
} 