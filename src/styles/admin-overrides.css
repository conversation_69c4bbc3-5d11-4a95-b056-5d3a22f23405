/* ====================
   ADMIN SHADCN OVERRIDES
   ==================== */

/* Override ShadcnUI components in admin context */
.admin-layout {
  /* Button Overrides */
  .btn,
  button[class*="inline-flex"] {
    @apply transition-all duration-200 ease-in-out;
  }
  
  /* Override primary buttons in admin */
  button[class*="bg-primary"],
  .btn-primary {
    background: linear-gradient(135deg, var(--admin-accent), var(--admin-secondary)) !important;
    color: var(--admin-text-white) !important;
    border: none !important;
    box-shadow: var(--admin-shadow-sm) !important;
  }
  
  button[class*="bg-primary"]:hover,
  .btn-primary:hover {
    box-shadow: var(--admin-shadow-md) !important;
    transform: translateY(-1px) !important;
  }
  
  /* Override secondary buttons */
  button[class*="variant-outline"],
  .btn-outline {
    background: var(--admin-bg-secondary) !important;
    color: var(--admin-text-primary) !important;
    border: 1px solid var(--admin-border-light) !important;
  }
  
  button[class*="variant-outline"]:hover,
  .btn-outline:hover {
    background: var(--admin-bg-tertiary) !important;
    border-color: var(--admin-border-medium) !important;
  }
  
  /* Card Overrides */
  div[class*="rounded-lg"][class*="border"],
  .card {
    background: var(--admin-bg-secondary) !important;
    border: 1px solid var(--admin-border-light) !important;
    border-radius: var(--admin-radius-lg) !important;
    box-shadow: var(--admin-shadow-sm) !important;
  }
  
  div[class*="rounded-lg"][class*="border"]:hover,
  .card:hover {
    box-shadow: var(--admin-shadow-md) !important;
    border-color: var(--admin-border-medium) !important;
  }
  
  /* Input Overrides */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  textarea,
  select {
    background: var(--admin-bg-secondary) !important;
    border: 1px solid var(--admin-border-light) !important;
    border-radius: var(--admin-radius-md) !important;
    color: var(--admin-text-primary) !important;
    padding: var(--admin-spacing-md) !important;
    transition: all 0.2s ease !important;
  }
  
  input[type="text"]:focus,
  input[type="email"]:focus,
  input[type="password"]:focus,
  input[type="number"]:focus,
  textarea:focus,
  select:focus {
    outline: none !important;
    border-color: var(--admin-accent) !important;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1) !important;
  }
  
  /* Table Overrides */
  table {
    background: var(--admin-bg-secondary) !important;
    border-radius: var(--admin-radius-lg) !important;
    overflow: hidden !important;
    box-shadow: var(--admin-shadow-sm) !important;
  }
  
  th {
    background: var(--admin-bg-tertiary) !important;
    color: var(--admin-text-primary) !important;
    font-weight: 600 !important;
    border-bottom: 1px solid var(--admin-border-light) !important;
  }
  
  td {
    color: var(--admin-text-secondary) !important;
    border-bottom: 1px solid var(--admin-border-light) !important;
  }
  
  tbody tr:hover {
    background: var(--admin-bg-tertiary) !important;
  }
  
  /* Badge/Status Overrides */
  span[class*="inline-flex"][class*="rounded"],
  .badge {
    padding: var(--admin-spacing-xs) var(--admin-spacing-sm) !important;
    border-radius: var(--admin-radius-sm) !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.025em !important;
  }
  
  /* Dialog/Modal Overrides */
  div[role="dialog"],
  .dialog {
    background: var(--admin-bg-secondary) !important;
    border: 1px solid var(--admin-border-light) !important;
    border-radius: var(--admin-radius-xl) !important;
    box-shadow: var(--admin-shadow-xl) !important;
  }
  
  /* Dropdown/Select Overrides */
  div[role="listbox"],
  div[role="menu"],
  .dropdown-content {
    background: var(--admin-bg-secondary) !important;
    border: 1px solid var(--admin-border-light) !important;
    border-radius: var(--admin-radius-lg) !important;
    box-shadow: var(--admin-shadow-lg) !important;
  }
  
  /* Checkbox and Radio Overrides */
  input[type="checkbox"],
  input[type="radio"] {
    accent-color: var(--admin-accent) !important;
  }
  
  /* Text Color Overrides */
  .text-foreground {
    color: var(--admin-text-primary) !important;
  }
  
  .text-muted-foreground {
    color: var(--admin-text-secondary) !important;
  }
  
  .text-primary {
    color: var(--admin-accent) !important;
  }
  
  /* Background Overrides */
  .bg-background {
    background-color: var(--admin-bg-primary) !important;
  }
  
  .bg-card {
    background-color: var(--admin-bg-secondary) !important;
  }
  
  .bg-muted {
    background-color: var(--admin-bg-tertiary) !important;
  }
  
  /* Border Overrides */
  .border {
    border-color: var(--admin-border-light) !important;
  }
  
  .border-border {
    border-color: var(--admin-border-light) !important;
  }
}

/* ====================
   ADMIN DARK MODE SUPPORT
   ==================== */

.admin-layout.dark {
  /* Dark mode variables */
  --admin-bg-primary: var(--admin-bg-dark);
  --admin-bg-secondary: #0f172a;
  --admin-bg-tertiary: #1e293b;
  --admin-bg-header: #0f172a;
  --admin-bg-sidebar: #020617;
  
  --admin-text-primary: var(--admin-text-light);
  --admin-text-secondary: #94a3b8;
  --admin-text-muted: #64748b;
  
  --admin-border-light: #334155;
  --admin-border-medium: #475569;
  --admin-border-dark: #64748b;
}

/* ====================
   ADMIN FOCUS STATES
   ==================== */

.admin-layout *:focus-visible {
  outline: 2px solid var(--admin-accent) !important;
  outline-offset: 2px !important;
}

/* ====================
   ADMIN SCROLLBAR
   ==================== */

.admin-layout ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.admin-layout ::-webkit-scrollbar-track {
  background: var(--admin-bg-tertiary);
}

.admin-layout ::-webkit-scrollbar-thumb {
  background: var(--admin-border-medium);
  border-radius: var(--admin-radius-sm);
}

.admin-layout ::-webkit-scrollbar-thumb:hover {
  background: var(--admin-text-secondary);
}

/* ====================
   ADMIN ANIMATION ENHANCEMNT
   ==================== */

.admin-layout * {
  transition-duration: 200ms !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .admin-layout * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
} 