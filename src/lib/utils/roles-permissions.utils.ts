import type { RoleWithPermissions } from "@/types/roles-permissions";

// Available resources
export const AVAILABLE_RESOURCES = [
  'profiles',
  'ivf_scores',
  'roles',
  'permissions',
  'users',
  'guest_sessions',
  'otps',
] as const;

// Permission action types
export const PERMISSION_ACTIONS = ['create', 'read', 'update', 'delete'] as const;

// Utility functions
export function getPermissionForResource(role: RoleWithPermissions, resource: string) {
  return role.permissions.find(p => p.resource === resource);
}

export function hasPermission(role: RoleWithPermissions, resource: string, action: string) {
  const permission = getPermissionForResource(role, resource);
  if (!permission) return false;
  return permission[`can_${action}` as keyof typeof permission] === true;
} 