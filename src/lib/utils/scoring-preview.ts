/**
 * Utility functions for previewing and testing scoring configurations
 * This helps admins understand how their scoring rules will work
 */

import { ScoringType, RangeScoreConfig, FormBuilderOption } from "@/types/fertility-questions";

export interface ScoringPreviewResult {
  input: any;
  score: number;
  explanation: string;
}

/**
 * Preview how range scoring will work with sample inputs
 */
export function previewRangeScoring(
  ranges: RangeScoreConfig[],
  sampleInputs: number[]
): ScoringPreviewResult[] {
  return sampleInputs.map(input => {
    let score = 0;
    let explanation = `Input ${input}: `;

    for (const range of ranges) {
      if (input >= range.min && input <= range.max) {
        score = range.score;
        explanation += `Falls in range ${range.min}-${range.max}, scores ${range.score} points`;
        break;
      }
    }

    if (score === 0) {
      explanation += "Does not fall in any defined range, scores 0 points";
    }

    return {
      input,
      score,
      explanation
    };
  });
}

/**
 * Preview how single choice scoring will work
 */
export function previewSingleChoiceScoring(
  options: FormBuilderOption[]
): ScoringPreviewResult[] {
  return options.map(option => ({
    input: option.option_text,
    score: option.score || 0,
    explanation: `Selecting "${option.option_text}" scores ${option.score || 0} points`
  }));
}

/**
 * Generate sample inputs for range scoring based on the configured ranges
 */
export function generateSampleInputs(ranges: RangeScoreConfig[]): number[] {
  if (ranges.length === 0) return [];

  const samples: number[] = [];
  
  // Add boundary values and midpoints for each range
  ranges.forEach(range => {
    samples.push(range.min); // Lower boundary
    samples.push(range.max); // Upper boundary
    samples.push(Math.round((range.min + range.max) / 2)); // Midpoint
  });

  // Add some values outside the ranges
  const minValue = Math.min(...ranges.map(r => r.min));
  const maxValue = Math.max(...ranges.map(r => r.max));
  
  if (minValue > 0) {
    samples.push(minValue - 1); // Below minimum
  }
  samples.push(maxValue + 1); // Above maximum

  // Remove duplicates and sort
  return [...new Set(samples)].sort((a, b) => a - b);
}

/**
 * Validate range scoring configuration
 */
export function validateRangeScoring(ranges: RangeScoreConfig[]): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (ranges.length === 0) {
    errors.push("At least one range is required");
    return { isValid: false, errors, warnings };
  }

  // Check for invalid ranges
  ranges.forEach((range, index) => {
    if (range.min >= range.max) {
      errors.push(`Range ${index + 1}: Minimum value must be less than maximum value`);
    }
    if (range.score < 0) {
      errors.push(`Range ${index + 1}: Score must be non-negative`);
    }
  });

  // Check for overlapping ranges
  for (let i = 0; i < ranges.length; i++) {
    for (let j = i + 1; j < ranges.length; j++) {
      const range1 = ranges[i];
      const range2 = ranges[j];
      
      if (range1.min <= range2.max && range1.max >= range2.min) {
        errors.push(`Ranges ${i + 1} and ${j + 1} overlap`);
      }
    }
  }

  // Check for gaps in coverage
  const sortedRanges = [...ranges].sort((a, b) => a.min - b.min);
  for (let i = 0; i < sortedRanges.length - 1; i++) {
    const currentMax = sortedRanges[i].max;
    const nextMin = sortedRanges[i + 1].min;
    
    if (nextMin > currentMax + 1) {
      warnings.push(`Gap between ranges: ${currentMax + 1} to ${nextMin - 1} has no score`);
    }
  }

  // Check for score consistency
  const scores = ranges.map(r => r.score);
  const uniqueScores = new Set(scores);
  if (uniqueScores.size === 1) {
    warnings.push("All ranges have the same score - consider if this is intentional");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validate single choice scoring configuration
 */
export function validateSingleChoiceScoring(options: FormBuilderOption[]): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (options.length === 0) {
    errors.push("At least one option is required");
    return { isValid: false, errors, warnings };
  }

  // Check for missing scores
  options.forEach((option, index) => {
    if (option.score === undefined || option.score === null) {
      errors.push(`Option ${index + 1} ("${option.option_text}"): Score is required`);
    } else if (option.score < 0) {
      errors.push(`Option ${index + 1} ("${option.option_text}"): Score must be non-negative`);
    }
  });

  // Check for duplicate option texts
  const optionTexts = options.map(o => o.option_text.toLowerCase());
  const duplicates = optionTexts.filter((text, index) => optionTexts.indexOf(text) !== index);
  if (duplicates.length > 0) {
    errors.push("Duplicate option texts found");
  }

  // Check for score patterns
  const scores = options.map(o => o.score || 0);
  const uniqueScores = new Set(scores);
  
  if (uniqueScores.size === 1) {
    warnings.push("All options have the same score - consider if this is intentional");
  }

  if (Math.max(...scores) === Math.min(...scores)) {
    warnings.push("No score variation between options");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Get scoring recommendations based on question type and content
 */
export function getScoringRecommendations(questionText: string, fieldType: string): {
  recommendedType: ScoringType;
  suggestions: string[];
} {
  const text = questionText.toLowerCase();
  
  // Recommend range scoring for numeric questions
  if (fieldType === 'NUMBER_INPUT' || fieldType === 'RANGE_SLIDER') {
    return {
      recommendedType: ScoringType.range,
      suggestions: [
        "Use range scoring for numeric inputs",
        "Consider age-related scoring (younger typically scores higher)",
        "For BMI, normal range (18.5-24.9) should score highest",
        "Ensure ranges cover all possible input values"
      ]
    };
  }

  // Recommend single choice for select fields
  if (fieldType === 'RADIO_SELECT' || fieldType === 'DROPDOWN_SELECT') {
    const suggestions = ["Use single choice scoring for predefined options"];
    
    if (text.includes('yes') || text.includes('no')) {
      suggestions.push("For Yes/No questions, typically Yes=1, No=0 or vice versa");
    }
    
    if (text.includes('frequency') || text.includes('often')) {
      suggestions.push("For frequency questions, higher frequency usually scores higher");
    }
    
    if (text.includes('stress') || text.includes('anxiety')) {
      suggestions.push("For stress-related questions, lower stress typically scores higher");
    }

    return {
      recommendedType: ScoringType.single_choice,
      suggestions
    };
  }

  // Default recommendation
  return {
    recommendedType: ScoringType.single_choice,
    suggestions: [
      "Choose scoring type based on your question format",
      "Range scoring for numeric inputs",
      "Single choice scoring for predefined options"
    ]
  };
}
