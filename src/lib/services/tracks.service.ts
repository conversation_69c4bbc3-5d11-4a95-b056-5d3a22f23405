import { PrismaClient } from "@/generated/prisma";
import type { Track } from "@/types/fertility-questions";
import { revalidatePath } from "next/cache";

const prisma = new PrismaClient();

/**
 * Get all available tracks
 */
export async function getAllTracks(): Promise<Track[]> {
  try {
    const tracks = await prisma.tracks.findMany({
      orderBy: {
        order: 'asc'
      }
    });
    return tracks;
  } catch (error) {
    console.error("Error fetching tracks:", error);
    throw new Error("Failed to fetch tracks");
  }
}

/**
 * Get tracks assigned to a specific question
 */
export async function getQuestionTracks(questionId: string): Promise<Track[]> {
  try {
    const questionTracks = await prisma.question_tracks.findMany({
      where: {
        question_id: questionId
      },
      include: {
        track: true
      },
      orderBy: {
        track: {
          order: 'asc'
        }
      }
    });
    
    return questionTracks.map(qt => qt.track);
  } catch (error) {
    console.error("Error fetching question tracks:", error);
    throw new Error("Failed to fetch question tracks");
  }
}

/**
 * Assign tracks to a question
 */
export async function assignTracksToQuestion(questionId: string, trackIds: string[]): Promise<void> {
  try {
    await prisma.$transaction(async (tx) => {
      // Remove existing track assignments
      await tx.question_tracks.deleteMany({
        where: {
          question_id: questionId
        }
      });

      // Add new track assignments
      if (trackIds.length > 0) {
        await tx.question_tracks.createMany({
          data: trackIds.map(trackId => ({
            question_id: questionId,
            track_id: trackId
          }))
        });
      }
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error assigning tracks to question:", error);
    throw new Error("Failed to assign tracks to question");
  }
}

/**
 * Get questions filtered by track
 */
export async function getQuestionsByTrack(trackId: string): Promise<any[]> {
  try {
    const questionTracks = await prisma.question_tracks.findMany({
      where: {
        track_id: trackId
      },
      include: {
        question: {
          include: {
            form: true,
            options: {
              orderBy: {
                order: 'asc'
              }
            }
          }
        }
      },
      orderBy: {
        question: {
          order: 'asc'
        }
      }
    });

    return questionTracks.map(qt => qt.question);
  } catch (error) {
    console.error("Error fetching questions by track:", error);
    throw new Error("Failed to fetch questions by track");
  }
}

/**
 * Get questions filtered by multiple tracks (OR condition)
 */
export async function getQuestionsByTracks(trackIds: string[]): Promise<any[]> {
  try {
    if (trackIds.length === 0) {
      return [];
    }

    const questionTracks = await prisma.question_tracks.findMany({
      where: {
        track_id: {
          in: trackIds
        }
      },
      include: {
        question: {
          include: {
            form: true,
            options: {
              orderBy: {
                order: 'asc'
              }
            },
            question_tracks: {
              include: {
                track: true
              }
            }
          }
        }
      },
      orderBy: {
        question: {
          order: 'asc'
        }
      }
    });

    // Remove duplicates and return unique questions
    const uniqueQuestions = new Map();
    questionTracks.forEach(qt => {
      if (!uniqueQuestions.has(qt.question.id)) {
        uniqueQuestions.set(qt.question.id, qt.question);
      }
    });

    return Array.from(uniqueQuestions.values());
  } catch (error) {
    console.error("Error fetching questions by tracks:", error);
    throw new Error("Failed to fetch questions by tracks");
  }
}

/**
 * Get track statistics
 */
export async function getTrackStatistics(): Promise<Array<{
  track: Track;
  questionCount: number;
}>> {
  try {
    const tracks = await getAllTracks();
    const statistics = [];

    for (const track of tracks) {
      const questionCount = await prisma.question_tracks.count({
        where: {
          track_id: track.id
        }
      });

      statistics.push({
        track,
        questionCount
      });
    }

    return statistics;
  } catch (error) {
    console.error("Error fetching track statistics:", error);
    throw new Error("Failed to fetch track statistics");
  }
}

/**
 * Check if a question is assigned to a specific track
 */
export async function isQuestionInTrack(questionId: string, trackId: string): Promise<boolean> {
  try {
    const assignment = await prisma.question_tracks.findFirst({
      where: {
        question_id: questionId,
        track_id: trackId
      }
    });

    return !!assignment;
  } catch (error) {
    console.error("Error checking question track assignment:", error);
    return false;
  }
}
