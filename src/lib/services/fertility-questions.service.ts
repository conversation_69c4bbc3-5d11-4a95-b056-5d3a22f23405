"use server";

import { PrismaClient } from "@/generated/prisma";
import type { 
  FormWithQuestions, 
  FormBuilderForm, 
  FormBuilderQuestion, 
  FormBuilderOption,
  FormCategory 
} from "@/types/fertility-questions";
import { revalidatePath } from "next/cache";
import { DEFAULT_FORM_NAMES } from "@/lib/constants/field-configs";

const prisma = new PrismaClient();

// Utility function to transform database question to FormBuilderQuestion
export function transformQuestionWithTracks(dbQuestion: any): FormBuilderQuestion {
  return {
    ...dbQuestion,
    tracks: dbQuestion.question_tracks?.map((qt: any) => qt.track_id) || [],
    scoring_config: dbQuestion.scoring_config ? JSON.parse(dbQuestion.scoring_config) : undefined
  };
}

// Get all forms with their questions and options
export async function getAllForms(): Promise<FormWithQuestions[]> {
  try {
    const forms = await prisma.forms.findMany({
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' }
            },
            question_tracks: {
              include: {
                track: true
              }
            }
          },
          orderBy: { order: 'asc' }
        }
      },
      orderBy: { created_at: 'asc' }
    });

    return forms;
  } catch (error) {
    console.error("Error fetching forms:", error);
    throw new Error("Failed to fetch forms");
  }
}

// Get a specific form by ID
export async function getFormById(id: string): Promise<FormWithQuestions | null> {
  try {
    const form = await prisma.forms.findUnique({
      where: { id },
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' }
            },
            question_tracks: {
              include: {
                track: true
              }
            }
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    return form;
  } catch (error) {
    console.error("Error fetching form:", error);
    throw new Error("Failed to fetch form");
  }
}

// Get form by name (for category-based forms)
export async function getFormByName(name: string): Promise<FormWithQuestions | null> {
  try {
    const form = await prisma.forms.findUnique({
      where: { name },
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' }
            },
            question_tracks: {
              include: {
                track: true
              }
            }
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    return form;
  } catch (error) {
    console.error("Error fetching form by name:", error);
    throw new Error("Failed to fetch form");
  }
}

// Create a new form
export async function createForm(formData: FormBuilderForm): Promise<FormWithQuestions> {
  try {
    const form = await prisma.forms.create({
      data: {
        name: formData.name,
        description: formData.description,
        questions: {
          create: formData.questions.map((question, index) => ({
            question_text: question.question_text,
            field_type: question.field_type,
            placeholder: question.placeholder,
            min_value: question.min_value,
            max_value: question.max_value,
            step: question.step,
            unit: question.unit,
            order: question.order || index,
            options: question.options ? {
              create: question.options.map((option, optIndex) => ({
                option_text: option.option_text,
                value: option.value,
                order: option.order || optIndex
              }))
            } : undefined
          }))
        }
      },
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' }
            }
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    revalidatePath('/admin/fertility-meter-questions');
    return form;
  } catch (error) {
    console.error("Error creating form:", error);
    throw new Error("Failed to create form");
  }
}

// Update an existing form
export async function updateForm(id: string, formData: Partial<FormBuilderForm>): Promise<FormWithQuestions> {
  try {
    const form = await prisma.forms.update({
      where: { id },
      data: {
        name: formData.name,
        description: formData.description,
      },
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' }
            }
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    revalidatePath('/admin/fertility-meter-questions');
    return form;
  } catch (error) {
    console.error("Error updating form:", error);
    throw new Error("Failed to update form");
  }
}

// Delete a form
export async function deleteForm(id: string): Promise<void> {
  try {
    await prisma.forms.delete({
      where: { id }
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error deleting form:", error);
    throw new Error("Failed to delete form");
  }
}

// Initialize default forms for the three categories if they don't exist
export async function initializeDefaultForms(): Promise<void> {
  try {
    const categories: FormCategory[] = ['biological', 'lifestyle', 'environmental'];

    for (const category of categories) {
      const formName = DEFAULT_FORM_NAMES[category];
      const existingForm = await prisma.forms.findUnique({
        where: { name: formName }
      });

      if (!existingForm) {
        await prisma.forms.create({
          data: {
            name: formName,
            description: `Default form for ${category} factors`,
            questions: {
              create: []
            }
          }
        });
      }
    }

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error initializing default forms:", error);
    throw new Error("Failed to initialize default forms");
  }
}

// Question management functions

// Add a new question to a form
export async function addQuestion(formId: string, questionData: FormBuilderQuestion): Promise<void> {
  try {
    // Get the current max order for questions in this form
    const maxOrderResult = await prisma.fertility_questions.aggregate({
      where: { form_id: formId },
      _max: { order: true }
    });

    const nextOrder = (maxOrderResult._max.order || 0) + 1;

    const createdQuestion = await prisma.fertility_questions.create({
      data: {
        form_id: formId,
        question_text: questionData.question_text,
        field_type: questionData.field_type,
        placeholder: questionData.placeholder,
        min_value: questionData.min_value,
        max_value: questionData.max_value,
        step: questionData.step,
        unit: questionData.unit,
        order: questionData.order || nextOrder,

        // Scoring fields
        scoring_type: questionData.scoring_type,
        scoring_config: questionData.scoring_config ? JSON.stringify(questionData.scoring_config) : undefined,

        options: questionData.options ? {
          create: questionData.options.map((option, index) => ({
            option_text: option.option_text,
            value: option.value,
            order: option.order || index,
            score: option.score
          }))
        } : undefined
      }
    });

    // Assign tracks if provided
    if (questionData.tracks && questionData.tracks.length > 0) {
      await prisma.question_tracks.createMany({
        data: questionData.tracks.map(trackId => ({
          question_id: createdQuestion.id,
          track_id: trackId
        }))
      });
    }

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error adding question:", error);
    throw new Error("Failed to add question");
  }
}

// Update an existing question
export async function updateQuestion(questionId: string, questionData: Partial<FormBuilderQuestion>): Promise<void> {
  try {
    await prisma.$transaction(async (tx) => {
      // Update the question
      await tx.fertility_questions.update({
        where: { id: questionId },
        data: {
          question_text: questionData.question_text,
          field_type: questionData.field_type,
          placeholder: questionData.placeholder,
          min_value: questionData.min_value,
          max_value: questionData.max_value,
          step: questionData.step,
          unit: questionData.unit,
          order: questionData.order,

          // Scoring fields
          scoring_type: questionData.scoring_type,
          scoring_config: questionData.scoring_config ? JSON.stringify(questionData.scoring_config) : undefined,
        }
      });

      // Update options if provided
      if (questionData.options !== undefined) {
        // Delete existing options
        await tx.form_options.deleteMany({
          where: { question_id: questionId }
        });

        // Create new options
        if (questionData.options.length > 0) {
          await tx.form_options.createMany({
            data: questionData.options.map((option, index) => ({
              question_id: questionId,
              option_text: option.option_text,
              value: option.value,
              order: option.order || index,
              score: option.score
            }))
          });
        }
      }

      // Update tracks if provided
      if (questionData.tracks !== undefined) {
        // Delete existing track assignments
        await tx.question_tracks.deleteMany({
          where: { question_id: questionId }
        });

        // Create new track assignments
        if (questionData.tracks.length > 0) {
          await tx.question_tracks.createMany({
            data: questionData.tracks.map(trackId => ({
              question_id: questionId,
              track_id: trackId
            }))
          });
        }
      }
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error updating question:", error);
    throw new Error("Failed to update question");
  }
}

// Delete a question
export async function deleteQuestion(questionId: string): Promise<void> {
  try {
    await prisma.fertility_questions.delete({
      where: { id: questionId }
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error deleting question:", error);
    throw new Error("Failed to delete question");
  }
}

// Reorder questions within a form
export async function reorderQuestions(formId: string, questionOrders: { id: string; order: number }[]): Promise<void> {
  try {
    await prisma.$transaction(
      questionOrders.map(({ id, order }) =>
        prisma.fertility_questions.update({
          where: { id },
          data: { order }
        })
      )
    );

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error reordering questions:", error);
    throw new Error("Failed to reorder questions");
  }
}

// Option management functions

// Add options to a question
export async function addOptionsToQuestion(questionId: string, options: FormBuilderOption[]): Promise<void> {
  try {
    await prisma.form_options.createMany({
      data: options.map((option, index) => ({
        question_id: questionId,
        option_text: option.option_text,
        value: option.value,
        order: option.order || index,
        score: option.score
      }))
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error adding options:", error);
    throw new Error("Failed to add options");
  }
}

// Update question options (replace all options)
export async function updateQuestionOptions(questionId: string, options: FormBuilderOption[]): Promise<void> {
  try {
    await prisma.$transaction(async (tx) => {
      // Delete existing options
      await tx.form_options.deleteMany({
        where: { question_id: questionId }
      });

      // Create new options
      if (options.length > 0) {
        await tx.form_options.createMany({
          data: options.map((option, index) => ({
            question_id: questionId,
            option_text: option.option_text,
            value: option.value,
            order: option.order || index,
            score: option.score
          }))
        });
      }
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error updating options:", error);
    throw new Error("Failed to update options");
  }
}

// Delete an option
export async function deleteOption(optionId: string): Promise<void> {
  try {
    await prisma.form_options.delete({
      where: { id: optionId }
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error deleting option:", error);
    throw new Error("Failed to delete option");
  }
}

// Utility function to get forms by category
export async function getFormsByCategory(): Promise<Record<FormCategory, FormWithQuestions | null>> {
  try {
    const categories: FormCategory[] = ['biological', 'lifestyle', 'environmental'];
    const result: Record<FormCategory, FormWithQuestions | null> = {
      biological: null,
      lifestyle: null,
      environmental: null
    };

    for (const category of categories) {
      const formName = DEFAULT_FORM_NAMES[category];
      result[category] = await getFormByName(formName);
    }

    return result;
  } catch (error) {
    console.error("Error fetching forms by category:", error);
    throw new Error("Failed to fetch forms by category");
  }
}
