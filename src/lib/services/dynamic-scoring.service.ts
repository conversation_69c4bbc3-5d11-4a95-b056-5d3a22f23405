import { PrismaClient, ScoringType } from "@/generated/prisma";
import type { RangeScoreConfig, SingleChoiceScoreConfig } from "@/types/fertility-questions";
import type { IVFScoreRow } from "@/types/ivf-score";

const prisma = new PrismaClient();

interface QuestionWithScoring {
  id: string;
  question_text: string;
  field_type: string;
  scoring_type: ScoringType | null;
  scoring_config: RangeScoreConfig[] | SingleChoiceScoreConfig[] | string | null;
  form: {
    name: string;
  };
  options: Array<{
    id: string;
    option_text: string;
    value: string | null;
    score: number | null;
  }>;
}

interface CategoryScores {
  biological: number;
  lifestyle: number;
  environmental: number;
}

/**
 * Fetch all fertility questions with their scoring configurations
 */
export async function getFertilityQuestionsWithScoring(): Promise<QuestionWithScoring[]> {
  try {
    console.log('Fetching fertility questions with scoring...');
    const questionsRaw = await prisma.fertility_questions.findMany({
      where: {
        scoring_type: {
          not: null
        }
      },
      include: {
        form: {
          select: {
            name: true
          }
        },
        options: {
          select: {
            id: true,
            option_text: true,
            value: true,
            score: true
          },
          orderBy: {
            order: 'asc'
          }
        }
      },
      orderBy: {
        order: 'asc'
      }
    });

    // Map scoring_config to correct type
    const questions: QuestionWithScoring[] = questionsRaw.map(q => {
      let scoring_config: string | RangeScoreConfig[] | SingleChoiceScoreConfig[] | null = null;
      if (typeof q.scoring_config === 'string' || q.scoring_config === null) {
        scoring_config = q.scoring_config;
      } else if (Array.isArray(q.scoring_config)) {
        // Try to detect RangeScoreConfig or SingleChoiceScoreConfig by property
        const filtered = q.scoring_config.filter(item => {
          if (item && typeof item === 'object') {
            return 'min' in item || 'option' in item;
          }
          return false;
        });
        scoring_config = filtered as unknown as RangeScoreConfig[] | SingleChoiceScoreConfig[];
      }
      return {
        ...q,
        scoring_config
      };
    });

    return questions;
  } catch (error) {
    console.error("Error fetching fertility questions with scoring:", error);
    throw new Error("Failed to fetch fertility questions with scoring");
  }
}

/**
 * Calculate score for a single question based on user's answer
 */
export function calculateQuestionScore(
  question: QuestionWithScoring,
  userAnswer: string | number | boolean
): number {
  if (!question.scoring_type || !question.scoring_config) {
    return 0;
  }

  try {
    // Parse scoring config if it's a string (from JSON field)
    let scoringConfig = question.scoring_config;
    if (typeof scoringConfig === 'string') {
      scoringConfig = JSON.parse(scoringConfig);
    }
    if (question.scoring_type === ScoringType.range) {
      if (typeof userAnswer === 'number') {
        if (Array.isArray(scoringConfig) && scoringConfig.length && 'min' in scoringConfig[0]) {
          return calculateRangeScore(scoringConfig as RangeScoreConfig[], userAnswer);
        } else if (typeof scoringConfig === 'string') {
          return calculateRangeScore(scoringConfig, userAnswer);
        }
      }
    } else if (question.scoring_type === ScoringType.single_choice) {
      if (typeof userAnswer === 'string' || typeof userAnswer === 'boolean') {
        return calculateSingleChoiceScore(question, String(userAnswer));
      }
    }
  } catch (error) {
    console.error(`Error calculating score for question ${question.id}:`, error);
  }

  return 0;
}

/**
 * Calculate score for range-based scoring
 */
function calculateRangeScore(scoringConfig: RangeScoreConfig[] | string, userAnswer: number): number {
  let ranges: RangeScoreConfig[] = [];
  if (typeof scoringConfig === 'string') {
    try {
      ranges = JSON.parse(scoringConfig) as RangeScoreConfig[];
    } catch {
      return 0;
    }
  } else if (Array.isArray(scoringConfig)) {
    ranges = scoringConfig;
  }
  if (typeof userAnswer !== 'number' || !Array.isArray(ranges)) {
    return 0;
  }
  for (const range of ranges) {
    if (userAnswer >= range.min && userAnswer <= range.max) {
      return range.score;
    }
  }
  return 0;
}

/**
 * Calculate score for single choice scoring
 */
function calculateSingleChoiceScore(
  question: QuestionWithScoring,
  userAnswer: string
): number {
  // First try to find score from options (for RADIO_SELECT/DROPDOWN_SELECT)
  const option = question.options.find(opt => 
    opt.option_text === userAnswer || opt.value === userAnswer
  );
  if (option && option.score !== null) {
    return option.score;
  }
  // Fallback to scoring_config if no option score found
  try {
    let scoringConfig = question.scoring_config;
    if (typeof scoringConfig === 'string') {
      scoringConfig = JSON.parse(scoringConfig);
    }
    if (Array.isArray(scoringConfig)) {
      const config = scoringConfig as SingleChoiceScoreConfig[];
      const scoreConfig = config.find(c => c.option === userAnswer);
      if (scoreConfig) {
        return scoreConfig.score;
      }
    }
  } catch (error) {
    console.error('Error parsing scoring config for single choice:', error);
  }
  return 0;
}

/**
 * Map IVF score data to question answers for scoring
 */
export function mapIVFDataToAnswers(data: IVFScoreRow): Record<string, string | number | boolean> {
  // Calculate BMI for BMI-related questions
  const bmi = data.weight / ((data.height / 100) ** 2);

  return {
    // Biological factors
    age: data.age,
    height: data.height,
    weight: data.weight,
    bmi: bmi,
    menstrual_regularity: data.menstrual_regularity,
    infertility_duration: data.infertility_duration,
    ivf_attempts: data.ivf_attempts,
    known_conditions: data.known_conditions,

    // Lifestyle factors
    stress_level: data.stress_level,
    diet_type: data.diet_type,
    exercise_frequency: data.exercise_frequency,
    sleep_quality: data.sleep_quality,
    emotional_support_at_home: data.emotional_support_at_home,
    smoking_or_alcohol_habits: data.smoking_or_alcohol_habits,

    // Environmental factors
    household_income_range: data.household_income_range,
    living_area: data.living_area,
    work_stress_level: data.work_stress_level,
    pollution_exposure: data.pollution_exposure,
    occupation_type: data.occupation_type,
  };
}

/**
 * Determine which category a form belongs to based on form name
 */
function getCategoryFromFormName(formName: string): keyof CategoryScores {
  const lowerName = formName.toLowerCase();
  
  if (lowerName.includes('biological') || lowerName.includes('physiological')) {
    return 'biological';
  } else if (lowerName.includes('lifestyle') || lowerName.includes('psychosocial')) {
    return 'lifestyle';
  } else if (lowerName.includes('environmental') || lowerName.includes('socioeconomic')) {
    return 'environmental';
  }
  
  // Default fallback - could be improved with more specific mapping
  return 'biological';
}

/**
 * Calculate dynamic IVF score using fertility questions configuration
 */
export async function calculateDynamicIVFScore(data: IVFScoreRow): Promise<{
  totalScore: number;
  maxScore: number;
  percentage: number;
  category: string;
  factors: CategoryScores;
}> {
  try {
    console.log('Starting dynamic IVF score calculation...');
    // Get all fertility questions with scoring
    const questions = await getFertilityQuestionsWithScoring();
    console.log(`Found ${questions.length} questions with scoring`);
    
    // Map user data to answers
    const userAnswers = mapIVFDataToAnswers(data);
    
    // Initialize category scores
    const categoryScores: CategoryScores = {
      biological: 0,
      lifestyle: 0,
      environmental: 0
    };

    // Calculate scores for each question
    for (const question of questions) {
      const questionKey = getQuestionKey(question.question_text);
      const userAnswer = userAnswers[questionKey];
      
      if (userAnswer !== undefined) {
        const score = calculateQuestionScore(question, userAnswer);
        const category = getCategoryFromFormName(question.form.name);
        categoryScores[category] += score;
      }
    }

    // Apply the IVF Fertility Meter formula from the instruction document
    // IVF-SPSNew = 100 × (0.5 × Pnew + 0.25 × Psynew + 0.25 × Enew)
    const normalizedBiological = Math.min(categoryScores.biological, 1); // Normalize to 0-1
    const normalizedLifestyle = Math.min(categoryScores.lifestyle, 1);
    const normalizedEnvironmental = Math.min(categoryScores.environmental, 1);
    
    const totalScore = 100 * (
      0.5 * normalizedBiological + 
      0.25 * normalizedLifestyle + 
      0.25 * normalizedEnvironmental
    );

    // Ensure minimum score of 60 as per instruction document
    const finalScore = Math.max(totalScore, 60);
    const maxScore = 100;
    const percentage = Math.round(finalScore);

    // Determine category based on percentage
    let category = "Low";
    if (percentage >= 80) category = "Excellent";
    else if (percentage >= 70) category = "Good";
    else if (percentage >= 60) category = "Fair";
    else if (percentage >= 50) category = "Moderate";

    return {
      totalScore: finalScore,
      maxScore,
      percentage,
      category,
      factors: categoryScores,
    };
  } catch (error) {
    console.error("Error calculating dynamic IVF score:", error);
    // Fallback to a basic score if dynamic calculation fails
    return {
      totalScore: 60,
      maxScore: 100,
      percentage: 60,
      category: "Fair",
      factors: {
        biological: 0,
        lifestyle: 0,
        environmental: 0
      }
    };
  }
}

/**
 * Convert question text to a key for mapping user answers
 * This is a simplified approach - in production, you might want to store
 * explicit mappings between questions and data fields
 */
function getQuestionKey(questionText: string): string {
  const text = questionText.toLowerCase();
  
  // Map common question patterns to data keys
  if (text.includes('age')) return 'age';
  if (text.includes('height')) return 'height';
  if (text.includes('weight')) return 'weight';
  if (text.includes('bmi')) return 'bmi';
  if (text.includes('menstrual')) return 'menstrual_regularity';
  if (text.includes('infertility') && text.includes('duration')) return 'infertility_duration';
  if (text.includes('ivf') && text.includes('attempt')) return 'ivf_attempts';
  if (text.includes('stress') && text.includes('level')) return 'stress_level';
  if (text.includes('diet')) return 'diet_type';
  if (text.includes('exercise')) return 'exercise_frequency';
  if (text.includes('sleep')) return 'sleep_quality';
  if (text.includes('emotional') && text.includes('support')) return 'emotional_support_at_home';
  if (text.includes('income')) return 'household_income_range';
  if (text.includes('living') && text.includes('area')) return 'living_area';
  if (text.includes('work') && text.includes('stress')) return 'work_stress_level';
  if (text.includes('pollution')) return 'pollution_exposure';
  if (text.includes('occupation')) return 'occupation_type';
  
  // Default fallback
  return questionText.toLowerCase().replace(/[^a-z0-9]/g, '_');
}
