import { FormFieldType, FieldConfig, FormCategoryConfig } from "@/types/fertility-questions";

// Field type configurations
export const FIELD_CONFIGS: Record<FormFieldType, FieldConfig> = {
  [FormFieldType.INPUT]: {
    type: FormFieldType.INPUT,
    label: "Text Input",
    description: "Free-text field for general input",
    supportsOptions: false,
    supportsRange: false,
    supportsPlaceholder: true,
    supportsUnit: false,
  },
  [FormFieldType.NUMBER_INPUT]: {
    type: FormFieldType.NUMBER_INPUT,
    label: "Number Input",
    description: "Numerical input with optional min/max values",
    supportsOptions: false,
    supportsRange: true,
    supportsPlaceholder: true,
    supportsUnit: true,
  },
  [FormFieldType.RADIO_SELECT]: {
    type: FormFieldType.RADIO_SELECT,
    label: "Radio Buttons",
    description: "Single selection from multiple options",
    supportsOptions: true,
    supportsRange: false,
    supportsPlaceholder: false,
    supportsUnit: false,
  },
  [FormFieldType.DROPDOWN_SELECT]: {
    type: FormFieldType.DROPDOWN_SELECT,
    label: "Dropdown Select",
    description: "Single selection from dropdown menu",
    supportsOptions: true,
    supportsRange: false,
    supportsPlaceholder: false,
    supportsUnit: false,
  },
  [FormFieldType.RANGE_SLIDER]: {
    type: FormFieldType.RANGE_SLIDER,
    label: "Range Slider",
    description: "Slider for selecting value within a range",
    supportsOptions: false,
    supportsRange: true,
    supportsPlaceholder: false,
    supportsUnit: true,
  },
};

// Form category configurations
export const FORM_CATEGORIES: Record<string, FormCategoryConfig> = {
  biological: {
    id: 'biological',
    name: 'Biological Factors',
    description: 'Questions related to biological and physiological factors affecting fertility',
    icon: 'Heart',
  },
  lifestyle: {
    id: 'lifestyle',
    name: 'Lifestyle & Psychosocial',
    description: 'Questions about lifestyle choices and psychological factors',
    icon: 'Brain',
  },
  environmental: {
    id: 'environmental',
    name: 'Environmental & Socioeconomic Factors',
    description: 'Questions about environmental exposures and socioeconomic factors',
    icon: 'Globe',
  },
};

// Default form names for each category
export const DEFAULT_FORM_NAMES = {
  biological: 'Biological Factors Form',
  lifestyle: 'Lifestyle & Psychosocial Form',
  environmental: 'Environmental & Socioeconomic Factors Form',
};

// Validation rules for different field types
export const FIELD_VALIDATION_RULES = {
  [FormFieldType.INPUT]: {
    maxLength: 500,
  },
  [FormFieldType.NUMBER_INPUT]: {
    min: -999999,
    max: 999999,
  },
  [FormFieldType.RADIO_SELECT]: {
    minOptions: 2,
    maxOptions: 10,
  },
  [FormFieldType.DROPDOWN_SELECT]: {
    minOptions: 2,
    maxOptions: 20,
  },
  [FormFieldType.RANGE_SLIDER]: {
    minRange: 1,
    maxRange: 1000,
  },
};

// Common units for number inputs and range sliders
export const COMMON_UNITS = [
  { value: 'yrs', label: 'Years' },
  { value: 'cm', label: 'Centimeters' },
  { value: 'kg', label: 'Kilograms' },
  { value: 'lbs', label: 'Pounds' },
  { value: 'ft', label: 'Feet' },
  { value: 'in', label: 'Inches' },
  { value: '%', label: 'Percentage' },
  { value: 'mg', label: 'Milligrams' },
  { value: 'ml', label: 'Milliliters' },
  { value: 'times', label: 'Times' },
  { value: 'days', label: 'Days' },
  { value: 'weeks', label: 'Weeks' },
  { value: 'months', label: 'Months' },
];
