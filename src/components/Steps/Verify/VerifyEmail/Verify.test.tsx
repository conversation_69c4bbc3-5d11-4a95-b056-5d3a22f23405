import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import VerifyEmail from "./Verify";

// Mock the OTPInput component
jest.mock("../../../shared/OTPInput/OTPInput", () => {
  return function MockOTPInput({
    length,
    onChange,
    onComplete,
    autoFocus,
  }: {
    length: number;
    onChange: (value: string) => void;
    onComplete?: (value: string) => void;
    autoFocus?: boolean;
  }) {
    return (
      <div data-testid="otp-input">
        <input
          data-testid="otp-field"
          onChange={(e) => onChange(e.target.value)}
          onBlur={() => onComplete && onComplete("12345")}
          placeholder={`Enter ${length} digit code`}
          autoFocus={autoFocus}
        />
      </div>
    );
  };
});

describe("VerifyEmail", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component", () => {
    render(<VerifyEmail />);
    expect(screen.getByText("Verify Email")).toBeInTheDocument();
  });

  it("renders the step header", () => {
    render(<VerifyEmail />);
    expect(screen.getByText("Verify Email")).toBeInTheDocument();
    expect(
      screen.getByText((content, element) => {
        return element?.textContent === "Step 5 of 5";
      })
    ).toBeInTheDocument();
  });

  it("renders the instruction text", () => {
    render(<VerifyEmail />);
    expect(screen.getByText("We've sent you a passcode.")).toBeInTheDocument();
    expect(
      screen.getByText("Please check your inbox at j*****@g***.com.")
    ).toBeInTheDocument();
  });

  it("renders the OTP input component", () => {
    render(<VerifyEmail />);
    expect(screen.getByTestId("otp-input")).toBeInTheDocument();
  });

  it("renders the resend code button", () => {
    render(<VerifyEmail />);
    expect(screen.getByText("Resend Code")).toBeInTheDocument();
  });

  it("renders the view my score button", () => {
    render(<VerifyEmail />);
    expect(screen.getByText("View My Score")).toBeInTheDocument();
  });

  it("disables view my score button initially", () => {
    render(<VerifyEmail />);
    const button = screen.getByText("View My Score");
    expect(button).toBeDisabled();
  });

  it("handles OTP input change", () => {
    render(<VerifyEmail />);
    const otpField = screen.getByTestId("otp-field");

    fireEvent.change(otpField, { target: { value: "12345" } });

    // The mock implementation doesn't actually enable the button,
    // but we can test that the OTP input is working
    expect(otpField).toHaveValue("12345");
  });

  it("handles resend code click", async () => {
    const consoleSpy = jest.spyOn(console, "log").mockImplementation(() => {});
    render(<VerifyEmail />);

    const resendButton = screen.getByText("Resend Code");

    fireEvent.click(resendButton);

    expect(screen.getByText("Resending...")).toBeInTheDocument();

    await waitFor(
      () => {
        expect(screen.getByText("Resend Code")).toBeInTheDocument();
      },
      { timeout: 3000 }
    );

    expect(consoleSpy).toHaveBeenCalledWith("Code resent");
    consoleSpy.mockRestore();
  });

  it("disables resend button while resending", async () => {
    render(<VerifyEmail />);

    const resendButton = screen.getByText("Resend Code");

    fireEvent.click(resendButton);

    const resendingButton = screen.getByText("Resending...");
    expect(resendingButton).toBeDisabled();
  });

  it("handles view my score button click when OTP is complete", () => {
    const consoleSpy = jest.spyOn(console, "log").mockImplementation(() => {});
    render(<VerifyEmail />);

    // Simulate OTP completion
    const otpField = screen.getByTestId("otp-field");
    fireEvent.change(otpField, { target: { value: "12345" } });
    fireEvent.blur(otpField); // Triggers onComplete

    // In a real implementation, this would enable the button
    // For now, we just test the console log
    expect(consoleSpy).toHaveBeenCalledWith("OTP completed:", "12345");
    consoleSpy.mockRestore();
  });

  it("renders NeedHelp component", () => {
    render(<VerifyEmail />);
    expect(screen.getByText(/Need Help/)).toBeInTheDocument();
  });

  it("renders Header and Footer components", () => {
    render(<VerifyEmail />);
    expect(screen.getByRole("banner")).toBeInTheDocument();
    expect(screen.getByRole("contentinfo")).toBeInTheDocument();
  });

  it("has proper layout structure", () => {
    const { container } = render(<VerifyEmail />);

    expect(container.firstChild).toHaveClass("min-h-screen");
    expect(container.firstChild).toHaveClass("flex");
    expect(container.firstChild).toHaveClass("flex-col");
  });

  it("displays masked email address", () => {
    render(<VerifyEmail />);
    expect(
      screen.getByText("Please check your inbox at j*****@g***.com.")
    ).toBeInTheDocument();
  });

  it("has centered text layout", () => {
    render(<VerifyEmail />);
    const instructions = screen
      .getByText("We've sent you a passcode.")
      .closest(".space-y-2");
    expect(instructions).toBeInTheDocument();
  });

  it("has proper button styling", () => {
    render(<VerifyEmail />);
    const button = screen.getByText("View My Score");
    expect(button).toHaveClass("w-full");
  });

  it("handles OTP input focus", () => {
    render(<VerifyEmail />);
    const otpField = screen.getByTestId("otp-field");
    expect(otpField).toHaveFocus();
  });

  it("maintains state across interactions", () => {
    render(<VerifyEmail />);

    const otpField = screen.getByTestId("otp-field");
    const resendButton = screen.getByText("Resend Code");

    // Change OTP
    fireEvent.change(otpField, { target: { value: "54321" } });
    expect(otpField).toHaveValue("54321");

    // Click resend
    fireEvent.click(resendButton);
    expect(screen.getByText("Resending...")).toBeInTheDocument();

    // OTP should still be there
    expect(otpField).toHaveValue("54321");
  });

  it("has proper accessibility attributes", () => {
    render(<VerifyEmail />);

    const resendButton = screen.getByText("Resend Code");
    const viewScoreButton = screen.getByText("View My Score");

    expect(resendButton).toHaveAttribute("type", "button");
    expect(viewScoreButton).toHaveAttribute("type", "button");
  });

  it("shows proper button states", () => {
    render(<VerifyEmail />);

    const resendButton = screen.getByText("Resend Code");
    const viewScoreButton = screen.getByText("View My Score");

    expect(resendButton).not.toBeDisabled();
    expect(viewScoreButton).toBeDisabled();
  });

  it("has proper spacing and layout", () => {
    render(<VerifyEmail />);

    const mainContent = screen
      .getByText("We've sent you a passcode.")
      .closest(".space-y-8");
    expect(mainContent).toBeInTheDocument();
    expect(mainContent).toHaveClass("text-center");
  });

  it("displays correct step information", () => {
    render(<VerifyEmail />);

    expect(
      screen.getByText((content, element) => {
        return element?.textContent === "Step 5 of 5";
      })
    ).toBeInTheDocument();
  });

  it("handles component mounting and unmounting", () => {
    const { unmount } = render(<VerifyEmail />);

    expect(screen.getByText("Verify Email")).toBeInTheDocument();

    unmount();

    expect(screen.queryByText("Verify Email")).not.toBeInTheDocument();
  });
});
