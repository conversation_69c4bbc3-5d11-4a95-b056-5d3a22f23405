import React, { useState } from "react";
import Header, { HeaderState } from "../../../shared/Header/Header";
import Footer from "../../../shared/Footer/Footer";
import Button, { ButtonType } from "../../../shared/Button/Button";
import NeedHelp from "../../../shared/NeedHelp/NeedHelp";
import StepHeader from "../../../shared/StepHeader/StepHeader";
import OTPInput from "../../../shared/OTPInput/OTPInput";

// Arrow right icon for the button
const ArrowRightIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M5 12h14" />
    <polyline points="12,5 19,12 12,19" />
  </svg>
);

const VerifyEmail = () => {
  const [otp, setOtp] = useState("");
  const [isResending, setIsResending] = useState(false);

  const handleOTPChange = (otpValue: string) => {
    setOtp(otpValue);
  };

  const handleOTPComplete = (otpValue: string) => {
    console.log("OTP completed:", otpValue);
  };

  const handleResendCode = () => {
    setIsResending(true);
    // Simulate resend API call
    setTimeout(() => {
      setIsResending(false);
      console.log("Code resent");
    }, 2000);
  };

  const handleViewMyScore = () => {
    if (otp.length === 5) {
      console.log("View My Score clicked with OTP:", otp);
    }
  };

  const maskedEmail = "j*****@g***.com";

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.LOGIN} />
      <main className="flex-1 flex justify-center py-4 px-1 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[40.125rem] w-full">
          <StepHeader currentStep={5} totalSteps={5} title="Verify Email" />

          <div className="bg-white rounded-lg">
            <div className="max-w-md mx-auto space-y-8 text-center">
              {/* Instructions */}
              <div className="space-y-2">
                <p className="text-[var(--grey-6)] text-base">
                  We&apos;ve sent you a passcode.
                </p>
                <p className="text-[var(--grey-6)] text-base">
                  Please check your inbox at {maskedEmail}.
                </p>
              </div>

              {/* OTP Input */}
              <div className="flex justify-center">
                <OTPInput
                  length={5}
                  onChange={handleOTPChange}
                  onComplete={handleOTPComplete}
                  autoFocus={true}
                />
              </div>

              {/* Resend Code Link */}
              <div>
                <button
                  type="button"
                  onClick={handleResendCode}
                  disabled={isResending}
                  className="text-[var(--grey-5)] text-base hover:text-[var(--grey-6)] transition-colors duration-200 underline disabled:opacity-50"
                >
                  {isResending ? "Resending..." : "Resend Code"}
                </button>
              </div>

              {/* View My Score Button */}
              <div className="mt-8">
                <Button
                  type={ButtonType.PRIMARY}
                  text="View My Score"
                  icon={<ArrowRightIcon />}
                  onClick={handleViewMyScore}
                  disabled={otp.length !== 5}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          <NeedHelp className="mt-8" />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default VerifyEmail;
