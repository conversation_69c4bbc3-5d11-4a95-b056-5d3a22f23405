import React, { useState } from "react";
import Header, { HeaderState } from "../../shared/Header/Header";
import Footer from "../../shared/Footer/Footer";
import Button, { ButtonType } from "../../shared/Button/Button";
import ToggleButton from "../../shared/ToggleButton/ToggleButton";
import NeedHelp from "../../shared/NeedHelp/NeedHelp";
import StepHeader from "../../shared/StepHeader/StepHeader";

const INCOME_RANGES = ["0-10k", "10k-50k", "50k-lack", "1 lack or above"];
const LIVING_AREAS = ["Urban", "Semi-urban", "Rural"];
const WORK_STRESS_LEVELS = ["Low", "Medium", "High"];
const POLLUTION_LEVELS = ["High", "Moderate", "Low"];
const OCCUPATION_TYPES = ["Desk job", "Field work", "Night shift", "Homemaker"];

const Environmental = () => {
  const [incomeRange, setIncomeRange] = useState("0-10k");
  const [livingArea, setLivingArea] = useState("Urban");
  const [workStressLevel, setWorkStressLevel] = useState("Low");
  const [pollutionExposure, setPollutionExposure] = useState("High");
  const [occupationType, setOccupationType] = useState("Desk job");

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.LOGIN} />
      <main className="flex-1 flex justify-center py-4 px-1 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[40.125rem] w-full">
          <StepHeader
            currentStep={3}
            totalSteps={5}
            title="Environmental & Socioeconomic Factors"
          />
          <div className="bg-white rounded-lg">
            <form className="space-y-6">
              {/* Household Income Range */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Household Income Range
                </label>
                <div className="flex gap-2 flex-wrap">
                  {INCOME_RANGES.map((range) => (
                    <ToggleButton
                      key={range}
                      isSelected={incomeRange === range}
                      onClick={() => setIncomeRange(range)}
                      variant="compact"
                    >
                      {range}
                    </ToggleButton>
                  ))}
                </div>
              </div>

              {/* Living Area */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Living Area
                </label>
                <div className="flex gap-2 flex-wrap">
                  {LIVING_AREAS.map((area) => (
                    <ToggleButton
                      key={area}
                      isSelected={livingArea === area}
                      onClick={() => setLivingArea(area)}
                      variant="compact"
                    >
                      {area}
                    </ToggleButton>
                  ))}
                </div>
              </div>

              {/* Work Stress Level */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Work Stress Level
                </label>
                <div className="flex gap-2 flex-wrap">
                  {WORK_STRESS_LEVELS.map((level) => (
                    <ToggleButton
                      key={level}
                      isSelected={workStressLevel === level}
                      onClick={() => setWorkStressLevel(level)}
                      variant="compact"
                    >
                      {level}
                    </ToggleButton>
                  ))}
                </div>
              </div>

              {/* Pollution Exposure */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Pollution Exposure
                </label>
                <div className="flex gap-2 flex-wrap">
                  {POLLUTION_LEVELS.map((level) => (
                    <ToggleButton
                      key={level}
                      isSelected={pollutionExposure === level}
                      onClick={() => setPollutionExposure(level)}
                      variant="compact"
                    >
                      {level}
                    </ToggleButton>
                  ))}
                </div>
              </div>

              {/* Occupation Type */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Occupation Type
                </label>
                <div className="flex gap-2 flex-wrap">
                  {OCCUPATION_TYPES.map((type) => (
                    <ToggleButton
                      key={type}
                      isSelected={occupationType === type}
                      onClick={() => setOccupationType(type)}
                      variant="compact"
                    >
                      {type}
                    </ToggleButton>
                  ))}
                </div>
              </div>

              {/* Buttons */}
              <div className="flex gap-4 justify-between mt-8">
                <Button
                  type={ButtonType.SECONDARY}
                  text="Cancel"
                  onClick={() => {}}
                  className="flex-1"
                />
                <Button
                  type={ButtonType.PRIMARY}
                  text="Next Step"
                  onClick={() => {}}
                  className="flex-1"
                />
              </div>
            </form>
          </div>
          <NeedHelp className="mt-8" />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Environmental;
