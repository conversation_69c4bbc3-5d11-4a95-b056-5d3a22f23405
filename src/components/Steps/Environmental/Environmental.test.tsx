import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import Environmental from "./Environmental";

describe("Environmental", () => {
  it("renders the component", () => {
    render(<Environmental />);
    expect(
      screen.getByText("Environmental & Socioeconomic Factors")
    ).toBeInTheDocument();
  });

  it("renders the step header", () => {
    render(<Environmental />);
    expect(
      screen.getByText("Environmental & Socioeconomic Factors")
    ).toBeInTheDocument();
    expect(
      screen.getByText((content, element) => {
        return element?.textContent === "Step 3 of 5";
      })
    ).toBeInTheDocument();
  });

  it("renders all form sections", () => {
    render(<Environmental />);

    expect(screen.getByText("Household Income Range")).toBeInTheDocument();
    expect(screen.getByText("Living Area")).toBeInTheDocument();
    expect(screen.getByText("Work Stress Level")).toBeInTheDocument();
    expect(screen.getByText("Pollution Exposure")).toBeInTheDocument();
    expect(screen.getByText("Occupation Type")).toBeInTheDocument();
  });

  it("renders household income range options", () => {
    render(<Environmental />);

    expect(screen.getByText("0-10k")).toBeInTheDocument();
    expect(screen.getByText("10k-50k")).toBeInTheDocument();
    expect(screen.getByText("50k-lack")).toBeInTheDocument();
    expect(screen.getByText("1 lack or above")).toBeInTheDocument();
  });

  it("renders living area options", () => {
    render(<Environmental />);

    expect(screen.getByText("Urban")).toBeInTheDocument();
    expect(screen.getByText("Semi-urban")).toBeInTheDocument();
    expect(screen.getByText("Rural")).toBeInTheDocument();
  });

  it("renders work stress level options", () => {
    render(<Environmental />);

    const lowButtons = screen.getAllByText("Low");
    const mediumButtons = screen.getAllByText("Medium");
    const highButtons = screen.getAllByText("High");

    expect(lowButtons.length).toBeGreaterThan(0);
    expect(mediumButtons.length).toBeGreaterThan(0);
    expect(highButtons.length).toBeGreaterThan(0);
  });

  it("renders pollution exposure options", () => {
    render(<Environmental />);

    const highButtons = screen.getAllByText("High");
    expect(screen.getByText("Moderate")).toBeInTheDocument();
    const lowButtons = screen.getAllByText("Low");

    expect(highButtons.length).toBeGreaterThan(0);
    expect(lowButtons.length).toBeGreaterThan(0);
  });

  it("renders occupation type options", () => {
    render(<Environmental />);

    expect(screen.getByText("Desk job")).toBeInTheDocument();
    expect(screen.getByText("Field work")).toBeInTheDocument();
    expect(screen.getByText("Night shift")).toBeInTheDocument();
    expect(screen.getByText("Homemaker")).toBeInTheDocument();
  });

  it("allows selection of different income ranges", () => {
    render(<Environmental />);

    const incomeButton = screen.getByText("10k-50k");
    fireEvent.click(incomeButton);

    expect(incomeButton).toBeInTheDocument();
  });

  it("allows selection of different living areas", () => {
    render(<Environmental />);

    const livingAreaButton = screen.getByText("Semi-urban");
    fireEvent.click(livingAreaButton);

    expect(livingAreaButton).toBeInTheDocument();
  });

  it("allows selection of different work stress levels", () => {
    render(<Environmental />);

    const workStressButton = screen.getByText("Medium");
    fireEvent.click(workStressButton);

    expect(workStressButton).toBeInTheDocument();
  });

  it("allows selection of different pollution exposure levels", () => {
    render(<Environmental />);

    const pollutionButton = screen.getByText("Moderate");
    fireEvent.click(pollutionButton);

    expect(pollutionButton).toBeInTheDocument();
  });

  it("allows selection of different occupation types", () => {
    render(<Environmental />);

    const occupationButton = screen.getByText("Field work");
    fireEvent.click(occupationButton);

    expect(occupationButton).toBeInTheDocument();
  });

  it("renders Cancel and Next Step buttons", () => {
    render(<Environmental />);

    expect(screen.getByText("Cancel")).toBeInTheDocument();
    expect(screen.getByText("Next Step")).toBeInTheDocument();
  });

  it("renders NeedHelp component", () => {
    render(<Environmental />);

    expect(screen.getByText(/Need Help/)).toBeInTheDocument();
  });

  it("renders Header and Footer components", () => {
    render(<Environmental />);

    expect(screen.getByRole("banner")).toBeInTheDocument();
    expect(screen.getByRole("contentinfo")).toBeInTheDocument();
  });

  it("has proper form structure", () => {
    render(<Environmental />);

    // Check that form elements exist
    const labels = screen.getAllByText(
      /Household Income Range|Living Area|Work Stress Level|Pollution Exposure|Occupation Type/
    );
    expect(labels.length).toBe(5);
  });

  it("has proper labels for form sections", () => {
    render(<Environmental />);

    expect(screen.getByText("Household Income Range")).toBeInTheDocument();
    expect(screen.getByText("Living Area")).toBeInTheDocument();
    expect(screen.getByText("Work Stress Level")).toBeInTheDocument();
    expect(screen.getByText("Pollution Exposure")).toBeInTheDocument();
    expect(screen.getByText("Occupation Type")).toBeInTheDocument();
  });

  it("has responsive layout classes", () => {
    const { container } = render(<Environmental />);

    expect(container.firstChild).toHaveClass("min-h-screen");
    expect(container.firstChild).toHaveClass("flex");
    expect(container.firstChild).toHaveClass("flex-col");
  });

  it("has toggle button functionality", () => {
    render(<Environmental />);

    const firstIncomeButton = screen.getByText("0-10k");
    const secondIncomeButton = screen.getByText("10k-50k");

    // Initially first button should be selected (but we won't test exact classes)
    expect(firstIncomeButton).toBeInTheDocument();
    expect(secondIncomeButton).toBeInTheDocument();

    // Click second button
    fireEvent.click(secondIncomeButton);

    // Both buttons should still be in the document
    expect(firstIncomeButton).toBeInTheDocument();
    expect(secondIncomeButton).toBeInTheDocument();
  });
});
