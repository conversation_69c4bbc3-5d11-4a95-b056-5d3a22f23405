import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import Signup from "./Signup";

describe("Signup", () => {
  it("renders the component", () => {
    render(<Signup />);
    expect(screen.getByText("Signup with Email")).toBeInTheDocument();
  });

  it("renders the step header", () => {
    render(<Signup />);
    expect(screen.getByText("Signup with Email")).toBeInTheDocument();
    expect(
      screen.getByText((content, element) => {
        return element?.textContent === "Step 4 of 5";
      })
    ).toBeInTheDocument();
  });

  it("renders all form fields", () => {
    render(<Signup />);

    expect(screen.getByLabelText("Name")).toBeInTheDocument();
    expect(screen.getByLabelText("Email")).toBeInTheDocument();
    expect(screen.getByLabelText("Number (Optional)")).toBeInTheDocument();
  });

  it("renders form fields with default values", () => {
    render(<Signup />);

    expect(screen.getByDisplayValue("Jyoti Mishra")).toBeInTheDocument();
    expect(
      screen.getByDisplayValue("<EMAIL>")
    ).toBeInTheDocument();
    expect(screen.getByDisplayValue("9012128888")).toBeInTheDocument();
  });

  it("renders consent checkbox", () => {
    render(<Signup />);

    const checkbox = screen.getByRole("checkbox");
    expect(checkbox).toBeInTheDocument();
    expect(
      screen.getByText("I consent to share my inputs for consultation purposes")
    ).toBeInTheDocument();
  });

  it("renders verify email button", () => {
    render(<Signup />);

    expect(screen.getByText("Verify Email")).toBeInTheDocument();
  });

  it("allows name input change", () => {
    render(<Signup />);

    const nameInput = screen.getByLabelText("Name");
    fireEvent.change(nameInput, { target: { value: "John Doe" } });

    expect(nameInput).toHaveValue("John Doe");
  });

  it("allows email input change", () => {
    render(<Signup />);

    const emailInput = screen.getByLabelText("Email");
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

    expect(emailInput).toHaveValue("<EMAIL>");
  });

  it("allows number input change", () => {
    render(<Signup />);

    const numberInput = screen.getByLabelText("Number (Optional)");
    fireEvent.change(numberInput, { target: { value: "1234567890" } });

    expect(numberInput).toHaveValue("1234567890");
  });

  it("handles consent checkbox toggle", () => {
    render(<Signup />);

    const checkbox = screen.getByRole("checkbox");

    // Initially unchecked
    expect(checkbox).not.toBeChecked();

    // Click to check
    fireEvent.click(checkbox);
    expect(checkbox).toBeChecked();

    // Click to uncheck
    fireEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();
  });

  it("disables verify email button when consent is not given", () => {
    render(<Signup />);

    const button = screen.getByText("Verify Email");
    expect(button).toBeDisabled();
  });

  it("enables verify email button when consent is given", () => {
    render(<Signup />);

    const checkbox = screen.getByRole("checkbox");
    const button = screen.getByText("Verify Email");

    fireEvent.click(checkbox);
    expect(button).not.toBeDisabled();
  });

  it("handles verify email button click when enabled", () => {
    const consoleSpy = jest.spyOn(console, "log").mockImplementation(() => {});
    render(<Signup />);

    const checkbox = screen.getByRole("checkbox");
    const button = screen.getByText("Verify Email");

    // Enable button by checking consent
    fireEvent.click(checkbox);

    // Click button
    fireEvent.click(button);

    expect(consoleSpy).toHaveBeenCalledWith("Verify Email clicked");
    consoleSpy.mockRestore();
  });

  it("renders NeedHelp component", () => {
    render(<Signup />);
    expect(screen.getByText(/Need Help/)).toBeInTheDocument();
  });

  it("renders Header and Footer components", () => {
    render(<Signup />);
    expect(screen.getByRole("banner")).toBeInTheDocument();
    expect(screen.getByRole("contentinfo")).toBeInTheDocument();
  });

  it("has proper form structure", () => {
    render(<Signup />);

    // Check that all form labels are present
    expect(screen.getByText("Name")).toBeInTheDocument();
    expect(screen.getByText("Email")).toBeInTheDocument();
    expect(screen.getByText("Number (Optional)")).toBeInTheDocument();
  });

  it("has responsive layout classes", () => {
    const { container } = render(<Signup />);

    expect(container.firstChild).toHaveClass("min-h-screen");
    expect(container.firstChild).toHaveClass("flex");
    expect(container.firstChild).toHaveClass("flex-col");
  });

  it("has proper input types", () => {
    render(<Signup />);

    const nameInput = screen.getByLabelText("Name");
    const emailInput = screen.getByLabelText("Email");
    const numberInput = screen.getByLabelText("Number (Optional)");

    expect(nameInput).toHaveAttribute("type", "text");
    expect(emailInput).toHaveAttribute("type", "email");
    expect(numberInput).toHaveAttribute("type", "tel");
  });

  it("maintains form state across interactions", () => {
    render(<Signup />);

    // Change all inputs
    const nameInput = screen.getByLabelText("Name");
    const emailInput = screen.getByLabelText("Email");
    const numberInput = screen.getByLabelText("Number (Optional)");
    const checkbox = screen.getByRole("checkbox");

    fireEvent.change(nameInput, { target: { value: "Jane Smith" } });
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(numberInput, { target: { value: "5551234567" } });
    fireEvent.click(checkbox);

    // Verify all changes are maintained
    expect(nameInput).toHaveValue("Jane Smith");
    expect(emailInput).toHaveValue("<EMAIL>");
    expect(numberInput).toHaveValue("5551234567");
    expect(checkbox).toBeChecked();
  });

  it("renders with proper accessibility attributes", () => {
    render(<Signup />);

    const checkbox = screen.getByRole("checkbox");
    const button = screen.getByRole("button", { name: /verify email/i });

    expect(checkbox).toHaveAttribute("id", "consent");
    expect(
      screen.getByLabelText(
        "I consent to share my inputs for consultation purposes"
      )
    ).toBeInTheDocument();
    expect(button).toHaveAttribute("type", "button");
  });

  it("has proper button styling", () => {
    render(<Signup />);

    const button = screen.getByText("Verify Email");
    expect(button).toHaveClass("w-full");
  });
});
