import React, { useState } from "react";
import Header, { HeaderState } from "../../shared/Header/Header";
import Footer from "../../shared/Footer/Footer";
import Button, { ButtonType } from "../../shared/Button/Button";
import Input from "../../shared/Input/Input";
import NeedHelp from "../../shared/NeedHelp/NeedHelp";
import StepHeader from "../../shared/StepHeader/StepHeader";

// Arrow right icon for the verify button
const ArrowRightIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M5 12h14" />
    <polyline points="12,5 19,12 12,19" />
  </svg>
);

const Signup = () => {
  const [name, setName] = useState("<PERSON><PERSON><PERSON>");
  const [email, setEmail] = useState("<EMAIL>");
  const [number, setNumber] = useState("9012128888");
  const [consent, setConsent] = useState(false);

  const handleVerifyEmail = () => {
    // Handle email verification
    console.log("Verify Email clicked");
  };

  const handleConsentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConsent(e.target.checked);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.LOGIN} />
      <main className="flex-1 flex justify-center py-4 px-1 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[40.125rem] w-full">
          <StepHeader
            currentStep={4}
            totalSteps={5}
            title="Signup with Email"
          />

          <div className="bg-white rounded-lg">
            <div className="max-w-md mx-auto space-y-6 flex flex-col gap-[4.625rem] p-6">
              {/* Name Field */}
              <div className="flex flex-col gap-4">
                <div>
                  <Input
                    type="text"
                    value={name}
                    onChange={setName}
                    label="Name"
                    placeholder="Enter your name"
                  />
                </div>

                {/* Email Field */}
                <div>
                  <Input
                    type="email"
                    value={email}
                    onChange={setEmail}
                    label="Email"
                    placeholder="Enter your email"
                  />
                </div>

                {/* Number Field (Optional) */}
                <div>
                  <Input
                    type="tel"
                    value={number}
                    onChange={setNumber}
                    label="Number (Optional)"
                    placeholder="Enter your phone number"
                  />
                </div>

                {/* Consent Checkbox */}
                <div className="flex items-start gap-3 mt-6">
                  <input
                    type="checkbox"
                    id="consent"
                    checked={consent}
                    onChange={handleConsentChange}
                    className="mt-1 h-4 w-4 text-[var(--violet-6)] focus:ring-[var(--violet-6)] border-[var(--grey-3)] rounded"
                  />
                  <label
                    htmlFor="consent"
                    className="text-[var(--grey-6)] text-sm leading-relaxed cursor-pointer"
                  >
                    I consent to share my inputs for consultation purposes
                  </label>
                </div>
              </div>
              {/* Verify Email Button */}
              <div className="mt-8">
                <Button
                  type={ButtonType.PRIMARY}
                  text="Verify Email"
                  icon={<ArrowRightIcon />}
                  onClick={handleVerifyEmail}
                  disabled={!consent}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          <NeedHelp className="mt-8" />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Signup;
