import type { Meta, StoryObj } from "@storybook/nextjs";
import Signup from "./Signup";

const meta: Meta<typeof Signup> = {
  title: "Steps/Signup",
  component: Signup,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const EmptyForm: Story = {
  args: {},
  render: () => {
    // This would show the form with empty fields
    // In a real implementation, you'd pass initial values as props
    return <Signup />;
  },
};

export const MobileView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
};

export const TabletView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
  },
};
