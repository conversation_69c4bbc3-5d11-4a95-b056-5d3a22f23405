import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import Lifestyle from "./Lifestyle";

describe("Lifestyle", () => {
  it("renders the component", () => {
    render(<Lifestyle />);
    expect(screen.getByText("Lifestyle & Psychosocial")).toBeInTheDocument();
  });

  it("renders the step header", () => {
    render(<Lifestyle />);
    expect(screen.getByText("Lifestyle & Psychosocial")).toBeInTheDocument();
    expect(
      screen.getByText((content, element) => {
        return element?.textContent === "Step 2 of 5";
      })
    ).toBeInTheDocument();
  });

  it("renders stress level progress bar", () => {
    render(<Lifestyle />);
    expect(screen.getByText("Stress Level")).toBeInTheDocument();
    const sliders = screen.getAllByRole("slider");
    expect(sliders.length).toBeGreaterThan(0);
  });

  it("renders height input with cm unit", () => {
    render(<Lifestyle />);
    expect(screen.getByText("cm")).toBeInTheDocument();
    expect(screen.getByDisplayValue("167.43")).toBeInTheDocument();
  });

  it("renders diet type options", () => {
    render(<Lifestyle />);
    const dietTypeLabels = screen.getAllByText("Diet Type");
    expect(dietTypeLabels.length).toBeGreaterThan(0);
    expect(screen.getByText("Balanced")).toBeInTheDocument();
    expect(screen.getByText("Vegetarian")).toBeInTheDocument();
    expect(screen.getByText("Junk-heavy")).toBeInTheDocument();
    expect(screen.getByText("Skipping meals")).toBeInTheDocument();
  });

  it("renders exercise frequency options", () => {
    render(<Lifestyle />);
    expect(screen.getByText("Exercise Frequency")).toBeInTheDocument();
    expect(screen.getByText("Daily")).toBeInTheDocument();
    expect(screen.getByText("2—3 times/week")).toBeInTheDocument();
    expect(screen.getByText("Rarely/Never")).toBeInTheDocument();
  });

  it("renders sleep quality progress bar", () => {
    render(<Lifestyle />);
    expect(screen.getByText("Sleep Quality")).toBeInTheDocument();
    // Should have multiple sliders (stress level and sleep quality)
    const sliders = screen.getAllByRole("slider");
    expect(sliders.length).toBe(2);
  });

  it("renders emotional support options", () => {
    render(<Lifestyle />);
    expect(screen.getByText("Emotional Support at Home")).toBeInTheDocument();
    expect(screen.getByText("Yes")).toBeInTheDocument();
    expect(screen.getByText("No")).toBeInTheDocument();
  });

  it("renders smoking/alcohol dropdown", () => {
    render(<Lifestyle />);
    expect(screen.getByText("Smoking or Alcohol Habits")).toBeInTheDocument();
    expect(screen.getByRole("combobox")).toBeInTheDocument();
  });

  it("renders smoking/alcohol dropdown options", () => {
    render(<Lifestyle />);
    const dropdown = screen.getByRole("combobox");
    expect(dropdown).toBeInTheDocument();

    // Check if the default option is selected
    expect(dropdown).toHaveValue("Smoking, Alcohol");
  });

  it("allows height input change", () => {
    render(<Lifestyle />);
    const heightInput = screen.getByDisplayValue("167.43");

    fireEvent.change(heightInput, { target: { value: "175.0" } });
    expect(heightInput).toHaveValue("175.0");
  });

  it("allows diet type selection", () => {
    render(<Lifestyle />);
    const vegetarianButton = screen.getByText("Vegetarian");

    fireEvent.click(vegetarianButton);
    expect(vegetarianButton).toBeInTheDocument();
  });

  it("allows exercise frequency selection", () => {
    render(<Lifestyle />);
    const exerciseButton = screen.getByText("2—3 times/week");

    fireEvent.click(exerciseButton);
    expect(exerciseButton).toBeInTheDocument();
  });

  it("allows emotional support selection", () => {
    render(<Lifestyle />);
    const noButton = screen.getByText("No");

    fireEvent.click(noButton);
    expect(noButton).toBeInTheDocument();
  });

  it("allows smoking/alcohol selection change", () => {
    render(<Lifestyle />);
    const dropdown = screen.getByRole("combobox");

    fireEvent.change(dropdown, { target: { value: "None" } });
    expect(dropdown).toHaveValue("None");
  });

  it("has stress level slider functionality", () => {
    render(<Lifestyle />);
    const sliders = screen.getAllByRole("slider");
    const stressSlider = sliders[0]; // First slider should be stress level

    fireEvent.change(stressSlider, { target: { value: "7" } });
    expect(stressSlider).toHaveValue("7");
  });

  it("has sleep quality slider functionality", () => {
    render(<Lifestyle />);
    const sliders = screen.getAllByRole("slider");
    const sleepSlider = sliders[1]; // Second slider should be sleep quality

    fireEvent.change(sleepSlider, { target: { value: "8" } });
    expect(sleepSlider).toHaveValue("8");
  });

  it("renders Cancel and Next Step buttons", () => {
    render(<Lifestyle />);
    expect(screen.getByText("Cancel")).toBeInTheDocument();
    expect(screen.getByText("Next Step")).toBeInTheDocument();
  });

  it("renders NeedHelp component", () => {
    render(<Lifestyle />);
    expect(screen.getByText(/Need Help/)).toBeInTheDocument();
  });

  it("renders Header and Footer components", () => {
    render(<Lifestyle />);
    expect(screen.getByRole("banner")).toBeInTheDocument();
    expect(screen.getByRole("contentinfo")).toBeInTheDocument();
  });

  it("has proper form structure", () => {
    render(<Lifestyle />);

    // Check that form elements exist
    const labels = screen.getAllByText(
      /Stress Level|Diet Type|Exercise Frequency|Sleep Quality|Emotional Support at Home|Smoking or Alcohol Habits/
    );
    expect(labels.length).toBe(7);
  });

  it("has responsive layout classes", () => {
    const { container } = render(<Lifestyle />);

    expect(container.firstChild).toHaveClass("min-h-screen");
    expect(container.firstChild).toHaveClass("flex");
    expect(container.firstChild).toHaveClass("flex-col");
  });

  it("has default values set correctly", () => {
    render(<Lifestyle />);

    // Check height default value
    expect(screen.getByDisplayValue("167.43")).toBeInTheDocument();

    // Check smoking dropdown default value
    const dropdown = screen.getByRole("combobox");
    expect(dropdown).toHaveValue("Smoking, Alcohol");

    // Check that "Balanced" diet type is selected by default
    expect(screen.getByText("Balanced")).toBeInTheDocument();
  });

  it("handles multiple toggle button interactions", () => {
    render(<Lifestyle />);

    // Test diet type toggle
    const vegetarianButton = screen.getByText("Vegetarian");
    const balancedButton = screen.getByText("Balanced");

    fireEvent.click(vegetarianButton);
    expect(vegetarianButton).toBeInTheDocument();
    expect(balancedButton).toBeInTheDocument();

    // Test exercise frequency toggle
    const rarelyButton = screen.getByText("Rarely/Never");
    fireEvent.click(rarelyButton);
    expect(rarelyButton).toBeInTheDocument();
  });

  it("handles dropdown option changes", () => {
    render(<Lifestyle />);
    const dropdown = screen.getByRole("combobox");

    // Test different dropdown options
    fireEvent.change(dropdown, { target: { value: "Smoking" } });
    expect(dropdown).toHaveValue("Smoking");

    fireEvent.change(dropdown, { target: { value: "Alcohol" } });
    expect(dropdown).toHaveValue("Alcohol");

    fireEvent.change(dropdown, { target: { value: "None" } });
    expect(dropdown).toHaveValue("None");
  });

  it("maintains form state across interactions", () => {
    render(<Lifestyle />);

    // Change height
    const heightInput = screen.getByDisplayValue("167.43");
    fireEvent.change(heightInput, { target: { value: "180.0" } });

    // Change diet type
    const vegetarianButton = screen.getByText("Vegetarian");
    fireEvent.click(vegetarianButton);

    // Change smoking habits
    const dropdown = screen.getByRole("combobox");
    fireEvent.change(dropdown, { target: { value: "None" } });

    // Verify all changes are maintained
    expect(heightInput).toHaveValue("180.0");
    expect(vegetarianButton).toBeInTheDocument();
    expect(dropdown).toHaveValue("None");
  });
});
