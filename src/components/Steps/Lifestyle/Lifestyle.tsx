import React, { useState } from "react";
import Header, { HeaderState } from "../../shared/Header/Header";
import Footer from "../../shared/Footer/Footer";
import Button, { ButtonType } from "../../shared/Button/Button";
import ToggleButton from "../../shared/ToggleButton/ToggleButton";
import Input from "../../shared/Input/Input";
import NeedHelp from "../../shared/NeedHelp/NeedHelp";
import ProgressBar from "../../shared/ProgressBar/ProgressBar";
import StepHeader from "@/components/shared/StepHeader/StepHeader";

const DIET_TYPES = ["Balanced", "Vegetarian", "Junk-heavy", "Skipping meals"];
const EXERCISE_FREQ = ["Daily", "2—3 times/week", "Rarely/Never"];

const SMOKING_OPTIONS = ["Smoking, Alcohol", "Smoking", "Alcohol", "None"];

const Lifestyle = () => {
  const [stressLevel, setStressLevel] = useState(4);
  const [dietType, setDietType] = useState("Balanced");
  const [height, setHeight] = useState("167.43");
  const [exercise, setExercise] = useState("Daily");
  const [sleepQuality, setSleepQuality] = useState(6);
  const [emotionalSupport, setEmotionalSupport] = useState("Yes");
  const [smoking, setSmoking] = useState("Smoking, Alcohol");

  // Map numeric value to sleep quality emoji
  const getSleepEmoji = (val: number) => {
    if (val <= 2) return "😴"; // Very tired
    if (val <= 4) return "😑"; // Tired
    if (val <= 6) return "🙂"; // Okay
    if (val <= 8) return "😊"; // Good
    return "😃"; // Excellent
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.LOGIN} />
      <main className="flex-1 flex justify-center py-4 px-1 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[40.125rem] w-full">
          <StepHeader
            currentStep={2}
            totalSteps={5}
            title="Lifestyle & Psychosocial"
          />
          <div className="bg-white rounded-lg">
            <form className="space-y-6">
              {/* Stress Level */}
              <ProgressBar
                label="Stress Level"
                valueType="number"
                startValue={0}
                endValue={10}
                currentValue={stressLevel}
                min={0}
                max={10}
                onChange={setStressLevel}
              />
              {/* Height */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Diet Type
                </label>
                <div className="relative">
                  <Input
                    type="text"
                    value={height}
                    onChange={setHeight}
                    className="pr-12"
                  />
                  <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[var(--grey-6)] text-base font-medium pointer-events-none">
                    cm
                  </span>
                </div>
              </div>
              {/* Diet Type */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Diet Type
                </label>
                <div className="flex gap-2 flex-wrap">
                  {DIET_TYPES.map((type) => (
                    <ToggleButton
                      key={type}
                      isSelected={dietType === type}
                      onClick={() => setDietType(type)}
                      variant="compact"
                    >
                      {type}
                    </ToggleButton>
                  ))}
                </div>
              </div>
              {/* Exercise Frequency */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Exercise Frequency
                </label>
                <div className="flex gap-2 flex-wrap">
                  {EXERCISE_FREQ.map((freq) => (
                    <ToggleButton
                      key={freq}
                      isSelected={exercise === freq}
                      onClick={() => setExercise(freq)}
                      variant="compact"
                    >
                      {freq}
                    </ToggleButton>
                  ))}
                </div>
              </div>
              {/* Sleep Quality */}
              <ProgressBar
                label="Sleep Quality"
                valueType="emoji"
                startValue="😴"
                endValue="😃"
                currentValue={sleepQuality}
                displayValue={getSleepEmoji(sleepQuality)}
                min={0}
                max={10}
                onChange={setSleepQuality}
              />
              {/* Emotional Support */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Emotional Support at Home
                </label>
                <div className="flex gap-2">
                  <ToggleButton
                    isSelected={emotionalSupport === "Yes"}
                    onClick={() => setEmotionalSupport("Yes")}
                    variant="compact"
                  >
                    Yes
                  </ToggleButton>
                  <ToggleButton
                    isSelected={emotionalSupport === "No"}
                    onClick={() => setEmotionalSupport("No")}
                    variant="compact"
                  >
                    No
                  </ToggleButton>
                </div>
              </div>
              {/* Smoking/Alcohol */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Smoking or Alcohol Habits
                </label>
                <select
                  className="w-full h-[3.125rem] px-4 py-3 border border-[var(--grey-3)] rounded-[0.25rem] text-[var(--grey-7)] text-base font-bold bg-white focus:outline-none focus:ring-2 focus:ring-[var(--violet-6)] focus:border-[var(--violet-6)] appearance-none"
                  value={smoking}
                  onChange={(e) => setSmoking(e.target.value)}
                  style={{
                    backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "right 1rem center",
                    backgroundSize: "1rem",
                  }}
                >
                  {SMOKING_OPTIONS.map((opt) => (
                    <option key={opt} value={opt}>
                      {opt}
                    </option>
                  ))}
                </select>
              </div>
              {/* Buttons */}
              <div className="flex gap-4 justify-between mt-8">
                <Button
                  type={ButtonType.SECONDARY}
                  text="Cancel"
                  onClick={() => {}}
                  className="flex-1"
                />
                <Button
                  type={ButtonType.PRIMARY}
                  text="Next Step"
                  onClick={() => {}}
                  className="flex-1"
                />
              </div>
            </form>
          </div>
          <NeedHelp className="mt-8" />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Lifestyle;
