"use client";

import React, { useState } from "react";
import { 
  FormWithQuestions, 
  FormBuilderQuestion, 
  FormCategory,
  QuestionWithOptions 
} from "@/types/fertility-questions";
import { FORM_CATEGORIES } from "@/lib/constants/field-configs";
import { <PERSON><PERSON> } from "@/components/ShadcnUI/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ShadcnUI/tabs";
import { Badge } from "@/components/ShadcnUI/badge";
import { Separator } from "@/components/ShadcnUI/separator";
import { Plus, RefreshCw, Heart, Brain, Globe } from "lucide-react";
import { QuestionBuilder } from "./QuestionBuilder";
import { QuestionList } from "./QuestionList";
import { toast } from "sonner";

interface FormBuilderProps {
  forms: Record<FormCategory, FormWithQuestions | null>;
  onSaveQuestion: (formId: string, question: FormBuilderQuestion) => Promise<void>;
  onUpdateQuestion: (questionId: string, question: Partial<FormBuilderQuestion>) => Promise<void>;
  onDeleteQuestion: (questionId: string) => Promise<void>;
  onReorderQuestions: (formId: string, questionOrders: { id: string; order: number }[]) => Promise<void>;
  onRefresh: () => Promise<void>;
  isLoading?: boolean;
}

const CATEGORY_ICONS = {
  biological: Heart,
  lifestyle: Brain,
  environmental: Globe,
};

export function FormBuilder({
  forms,
  onSaveQuestion,
  onUpdateQuestion,
  onDeleteQuestion,
  onReorderQuestions,
  onRefresh,
  isLoading = false
}: FormBuilderProps) {
  const [activeTab, setActiveTab] = useState<FormCategory>('biological');
  const [showQuestionBuilder, setShowQuestionBuilder] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<QuestionWithOptions | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const currentForm = forms[activeTab];
  const currentQuestions = currentForm?.questions || [];

  const handleAddQuestion = () => {
    setEditingQuestion(null);
    setShowQuestionBuilder(true);
  };

  const handleEditQuestion = (question: QuestionWithOptions) => {
    setEditingQuestion(question);
    setShowQuestionBuilder(true);
  };

  const handleSaveQuestion = async (question: FormBuilderQuestion) => {
    if (!currentForm) {
      toast.error("No form selected");
      return;
    }

    setIsSaving(true);
    try {
      if (editingQuestion) {
        await onUpdateQuestion(editingQuestion.id, question);
        toast.success("Question updated successfully");
      } else {
        await onSaveQuestion(currentForm.id, question);
        toast.success("Question added successfully");
      }
      setShowQuestionBuilder(false);
      setEditingQuestion(null);
    } catch (error) {
      toast.error(editingQuestion ? "Failed to update question" : "Failed to add question");
      console.error("Error saving question:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteQuestion = async (questionId: string) => {
    setIsSaving(true);
    try {
      await onDeleteQuestion(questionId);
      toast.success("Question deleted successfully");
    } catch (error) {
      toast.error("Failed to delete question");
      console.error("Error deleting question:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReorderQuestions = async (questionOrders: { id: string; order: number }[]) => {
    if (!currentForm) return;

    try {
      await onReorderQuestions(currentForm.id, questionOrders);
      toast.success("Questions reordered successfully");
    } catch (error) {
      toast.error("Failed to reorder questions");
      console.error("Error reordering questions:", error);
    }
  };

  const handleCancel = () => {
    setShowQuestionBuilder(false);
    setEditingQuestion(null);
  };

  const handleRefresh = async () => {
    try {
      await onRefresh();
      toast.success("Forms refreshed successfully");
    } catch (error) {
      toast.error("Failed to refresh forms");
      console.error("Error refreshing forms:", error);
    }
  };

  if (showQuestionBuilder) {
    return (
      <QuestionBuilder
        question={editingQuestion ? {
          id: editingQuestion.id,
          question_text: editingQuestion.question_text,
          field_type: editingQuestion.field_type,
          placeholder: editingQuestion.placeholder || undefined,
          min_value: editingQuestion.min_value || undefined,
          max_value: editingQuestion.max_value || undefined,
          step: editingQuestion.step || undefined,
          unit: editingQuestion.unit || undefined,
          order: editingQuestion.order,
          options: editingQuestion.options.map(opt => ({
            id: opt.id,
            option_text: opt.option_text,
            value: opt.value || undefined,
            order: opt.order
          }))
        } : undefined}
        onSave={handleSaveQuestion}
        onCancel={handleCancel}
        isEditing={!!editingQuestion}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Fertility Meter Questions</h1>
          <p className="text-muted-foreground mt-1">
            Configure and manage questions for the fertility assessment
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
            className="gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Form Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as FormCategory)}>
        <TabsList className="grid w-full grid-cols-3">
          {Object.entries(FORM_CATEGORIES).map(([key, config]) => {
            const Icon = CATEGORY_ICONS[key as FormCategory];
            const form = forms[key as FormCategory];
            const questionCount = form?.questions.length || 0;
            
            return (
              <TabsTrigger key={key} value={key} className="gap-2">
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">{config.name}</span>
                <Badge variant="secondary" className="ml-1">
                  {questionCount}
                </Badge>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {Object.entries(FORM_CATEGORIES).map(([key, config]) => {
          const categoryKey = key as FormCategory;
          const form = forms[categoryKey];
          const Icon = CATEGORY_ICONS[categoryKey];

          return (
            <TabsContent key={key} value={key} className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <Icon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle>{config.name}</CardTitle>
                        <p className="text-sm text-muted-foreground mt-1">
                          {config.description}
                        </p>
                      </div>
                    </div>
                    <Button
                      onClick={handleAddQuestion}
                      disabled={!form || isSaving}
                      className="gap-2"
                      variant="default"
                    >
                      <Plus className="h-4 w-4" />
                      Add Question
                    </Button>
                  </div>
                </CardHeader>
                
                {form && (
                  <>
                    <Separator />
                    <CardContent className="pt-6">
                      <QuestionList
                        questions={currentQuestions}
                        onEditQuestion={handleEditQuestion}
                        onDeleteQuestion={handleDeleteQuestion}
                        onReorderQuestions={handleReorderQuestions}
                        isLoading={isLoading || isSaving}
                      />
                    </CardContent>
                  </>
                )}
              </Card>

              {!form && (
                <Card>
                  <CardContent className="p-8 text-center">
                    <p className="text-muted-foreground">
                      Form not found. Please refresh the page or contact support.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          );
        })}
      </Tabs>
    </div>
  );
}
