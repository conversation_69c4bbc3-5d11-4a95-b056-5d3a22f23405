"use client";

import React, { useState } from "react";
import { FormFieldType, FormBuilderQuestion, FormBuilderOption, ScoringType, RangeScoreConfig } from "@/types/fertility-questions";
import { FIELD_CONFIGS, COMMON_UNITS } from "@/lib/constants/field-configs";
import { But<PERSON> } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { Textarea } from "@/components/ShadcnUI/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ShadcnUI/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";
import { Separator } from "@/components/ShadcnUI/separator";
import { Plus, Trash2, Save, X } from "lucide-react";
import { FormFieldPreview } from "./FormFieldRenderer";
import { ScoringTypeSelector } from "./ScoringTypeSelector";
import { RangeScoreConfigComponent } from "./RangeScoreConfig";
import { SingleChoiceScoreConfig } from "./SingleChoiceScoreConfig";

interface QuestionBuilderProps {
  question?: FormBuilderQuestion;
  onSave: (question: FormBuilderQuestion) => void;
  onCancel: () => void;
  isEditing?: boolean;
}

export function QuestionBuilder({
  question,
  onSave,
  onCancel,
  isEditing = false
}: QuestionBuilderProps) {
  const [formData, setFormData] = useState<FormBuilderQuestion>({
    question_text: question?.question_text || "",
    field_type: question?.field_type || FormFieldType.INPUT,
    placeholder: question?.placeholder || "",
    min_value: question?.min_value,
    max_value: question?.max_value,
    step: question?.step,
    unit: question?.unit || "",
    order: question?.order || 0,
    options: question?.options || [],
    scoring_type: question?.scoring_type,
    scoring_config: question?.scoring_config || [],
    ...question
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const fieldConfig = FIELD_CONFIGS[formData.field_type];

  const handleFieldChange = (field: keyof FormBuilderQuestion, value: string | number | FormBuilderOption[] | undefined) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const handleOptionChange = (index: number, field: keyof FormBuilderOption, value: string) => {
    const newOptions = [...(formData.options || [])];
    // Convert empty string to undefined for the value field
    const processedValue = field === 'value' && value === '' ? undefined : value;
    newOptions[index] = { ...newOptions[index], [field]: processedValue };
    handleFieldChange('options', newOptions);
  };

  const addOption = () => {
    const newOptions = [...(formData.options || [])];
    newOptions.push({
      option_text: "",
      value: undefined, // Use undefined instead of empty string
      order: newOptions.length
    });
    handleFieldChange('options', newOptions);
  };

  const removeOption = (index: number) => {
    const newOptions = [...(formData.options || [])];
    newOptions.splice(index, 1);
    // Reorder remaining options
    newOptions.forEach((option, idx) => {
      option.order = idx;
    });
    handleFieldChange('options', newOptions);
  };

  // Scoring handlers
  const handleScoringTypeChange = (scoringType: ScoringType) => {
    setFormData(prev => ({
      ...prev,
      scoring_type: scoringType,
      scoring_config: scoringType === ScoringType.range ? [] : []
    }));
  };

  const handleRangeScoreConfigChange = (config: RangeScoreConfig[]) => {
    setFormData(prev => ({
      ...prev,
      scoring_config: config
    }));
  };

  const handleOptionsWithScoreChange = (options: FormBuilderOption[]) => {
    setFormData(prev => ({
      ...prev,
      options: options
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.question_text.trim()) {
      newErrors.question_text = "Question text is required";
    }

    if (fieldConfig.supportsOptions && (!formData.options || formData.options.length < 2)) {
      newErrors.options = "At least 2 options are required";
    }

    if (fieldConfig.supportsOptions && formData.options) {
      formData.options.forEach((option, index) => {
        if (!option.option_text.trim()) {
          newErrors[`option_${index}`] = "Option text is required";
        }
      });
    }

    if (fieldConfig.supportsRange) {
      if (formData.min_value === undefined || formData.max_value === undefined) {
        newErrors.range = "Both minimum and maximum values are required";
      } else if (formData.min_value >= formData.max_value) {
        newErrors.range = "Minimum value must be less than maximum value";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>
            {isEditing ? "Edit Question" : "Create New Question"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Question Text */}
          <div className="space-y-2">
            <Label htmlFor="question_text">Question Text *</Label>
            <Textarea
              id="question_text"
              placeholder="Enter your question..."
              value={formData.question_text}
              onChange={(e) => handleFieldChange('question_text', e.target.value)}
              className={errors.question_text ? "border-red-500" : ""}
            />
            {errors.question_text && (
              <p className="text-sm text-red-500">{errors.question_text}</p>
            )}
          </div>

          {/* Field Type */}
          <div className="space-y-2">
            <Label htmlFor="field_type">Field Type *</Label>
            <Select
              value={formData.field_type}
              onValueChange={(value) => handleFieldChange('field_type', value as FormFieldType)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.values(FormFieldType).map((type) => (
                  <SelectItem key={type} value={type}>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{FIELD_CONFIGS[type].label}</Badge>
                      <span className="text-sm text-muted-foreground">
                        {FIELD_CONFIGS[type].description}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Placeholder (for INPUT and NUMBER_INPUT) */}
          {fieldConfig.supportsPlaceholder && (
            <div className="space-y-2">
              <Label htmlFor="placeholder">Placeholder Text</Label>
              <Input
                id="placeholder"
                placeholder="Enter placeholder text..."
                value={formData.placeholder || ""}
                onChange={(e) => handleFieldChange('placeholder', e.target.value)}
              />
            </div>
          )}

          {/* Range Values (for NUMBER_INPUT and RANGE_SLIDER) */}
          {fieldConfig.supportsRange && (
            <div className="space-y-2">
              <Label>Value Range *</Label>
              <div className="grid grid-cols-3 gap-2">
                <div>
                  <Label htmlFor="min_value" className="text-sm">Minimum</Label>
                  <Input
                    id="min_value"
                    type="number"
                    placeholder="Min"
                    value={formData.min_value || ""}
                    onChange={(e) => handleFieldChange('min_value', parseInt(e.target.value) || undefined)}
                  />
                </div>
                <div>
                  <Label htmlFor="max_value" className="text-sm">Maximum</Label>
                  <Input
                    id="max_value"
                    type="number"
                    placeholder="Max"
                    value={formData.max_value || ""}
                    onChange={(e) => handleFieldChange('max_value', parseInt(e.target.value) || undefined)}
                  />
                </div>
                <div>
                  <Label htmlFor="step" className="text-sm">Step</Label>
                  <Input
                    id="step"
                    type="number"
                    placeholder="1"
                    value={formData.step || ""}
                    onChange={(e) => handleFieldChange('step', parseInt(e.target.value) || undefined)}
                  />
                </div>
              </div>
              {errors.range && (
                <p className="text-sm text-red-500">{errors.range}</p>
              )}
            </div>
          )}

          {/* Unit (for NUMBER_INPUT and RANGE_SLIDER) */}
          {fieldConfig.supportsUnit && (
            <div className="space-y-2">
              <Label htmlFor="unit">Unit</Label>
              <Select
                value={formData.unit || "none"}
                onValueChange={(value) => handleFieldChange('unit', value === "none" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select unit (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No unit</SelectItem>
                  {COMMON_UNITS.map((unit) => (
                    <SelectItem key={unit.value} value={unit.value}>
                      {unit.label} ({unit.value})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Options (for RADIO_SELECT and DROPDOWN_SELECT) */}
          {fieldConfig.supportsOptions && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Options *</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addOption}
                  className="gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Option
                </Button>
              </div>
              
              <div className="space-y-2">
                {formData.options?.map((option, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      placeholder="Option text"
                      value={option.option_text}
                      onChange={(e) => handleOptionChange(index, 'option_text', e.target.value)}
                      className={errors[`option_${index}`] ? "border-red-500" : ""}
                    />
                    <Input
                      placeholder="Value (optional)"
                      value={option.value || ""}
                      onChange={(e) => handleOptionChange(index, 'value', e.target.value)}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeOption(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              
              {errors.options && (
                <p className="text-sm text-red-500">{errors.options}</p>
              )}
              
              {formData.options?.some((_, index) => errors[`option_${index}`]) && (
                <p className="text-sm text-red-500">All options must have text</p>
              )}
            </div>
          )}

          <Separator />

          {/* Scoring Configuration */}
          <div className="space-y-4">
            <div>
              <Label className="text-lg font-semibold">IVF Fertility Meter Scoring</Label>
              <p className="text-sm text-muted-foreground mt-1">
                Configure how this question contributes to the overall fertility meter score.
              </p>
            </div>

            <ScoringTypeSelector
              value={formData.scoring_type}
              onChange={handleScoringTypeChange}
            />

            {formData.scoring_type === ScoringType.range && (
              <RangeScoreConfigComponent
                value={formData.scoring_config as RangeScoreConfig[] || []}
                onChange={handleRangeScoreConfigChange}
              />
            )}

            {formData.scoring_type === ScoringType.single_choice && (
              <SingleChoiceScoreConfig
                options={formData.options || []}
                onOptionsChange={handleOptionsWithScoreChange}
              />
            )}
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Button onClick={handleSave} className="gap-2">
              <Save className="h-4 w-4" />
              {isEditing ? "Update Question" : "Create Question"}
            </Button>
            <Button variant="outline" onClick={onCancel} className="gap-2">
              <X className="h-4 w-4" />
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Preview */}
      {formData.question_text && (
        <div>
          <Label className="text-base font-medium mb-2 block">Preview</Label>
          <FormFieldPreview
            question={{
              ...formData,
              id: 'preview',
              form_id: 'preview',
              created_at: new Date(),
              updated_at: new Date(),
              min_value: formData.min_value ?? null,
              max_value: formData.max_value ?? null,
              step: formData.step ?? null,
              unit: formData.unit ?? null,
              placeholder: formData.placeholder ?? null,
              scoring_type: formData.scoring_type ?? null,
              scoring_config: formData.scoring_config ? JSON.parse(JSON.stringify(formData.scoring_config)) : null,
              options: formData.options?.map((opt, idx) => ({
                ...opt,
                id: `preview-${idx}`,
                question_id: 'preview',
                created_at: new Date(),
                updated_at: new Date(),
                value: opt.value ?? null,
                score: opt.score ?? null,
              })) || []
            }}
          />
        </div>
      )}
    </div>
  );
}
