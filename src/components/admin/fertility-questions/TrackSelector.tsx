"use client";

import React, { useState, useEffect } from "react";
import { Track } from "@/types/fertility-questions";
import { Label } from "@/components/ShadcnUI/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";
import { Checkbox } from "@/components/ShadcnUI/checkbox";
import { AlertCircle, Users, Target } from "lucide-react";
import { getAllTracks } from "@/lib/services/tracks.service";

interface TrackSelectorProps {
  selectedTracks: string[];
  onChange: (trackIds: string[]) => void;
  disabled?: boolean;
}

export function TrackSelector({
  selectedTracks,
  onChange,
  disabled = false
}: TrackSelectorProps) {
  const [tracks, setTracks] = useState<Track[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTracks = async () => {
      try {
        setLoading(true);
        const fetchedTracks = await getAllTracks();
        setTracks(fetchedTracks);
        setError(null);
      } catch (err) {
        console.error("Error fetching tracks:", err);
        setError("Failed to load tracks");
      } finally {
        setLoading(false);
      }
    };

    fetchTracks();
  }, []);

  const handleTrackToggle = (trackId: string, checked: boolean) => {
    if (checked) {
      onChange([...selectedTracks, trackId]);
    } else {
      onChange(selectedTracks.filter(id => id !== trackId));
    }
  };

  if (loading) {
    return (
      <div className="space-y-2">
        <Label className="text-base font-medium">Question Tracks</Label>
        <Card className="border-dashed">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-sm text-muted-foreground">Loading tracks...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-2">
        <Label className="text-base font-medium">Question Tracks</Label>
        <Card className="border-red-200 bg-red-50">
          <CardContent className="py-4">
            <div className="flex items-center gap-2 text-red-600 text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div>
        <Label className="text-base font-medium">Question Tracks</Label>
        <p className="text-sm text-muted-foreground mt-1">
          Select which tracks this question should appear in. Users will only see questions from their selected track.
        </p>
      </div>

      <div className="space-y-3">
        {tracks.map((track) => {
          const isSelected = selectedTracks.includes(track.id);
          
          return (
            <Card 
              key={track.id} 
              className={`cursor-pointer transition-colors ${
                isSelected ? "border-primary bg-primary/5" : "hover:border-primary/50"
              } ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
              onClick={() => !disabled && handleTrackToggle(track.id, !isSelected)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={(checked) => 
                        !disabled && handleTrackToggle(track.id, checked as boolean)
                      }
                      disabled={disabled}
                      onClick={(e) => e.stopPropagation()}
                    />
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4 text-primary" />
                      <CardTitle className="text-base">{track.name}</CardTitle>
                      <Badge variant="secondary" className="text-xs">
                        {track.code}
                      </Badge>
                    </div>
                  </div>
                  {isSelected && (
                    <Badge variant="default" className="text-xs">
                      Selected
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {track.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {selectedTracks.length === 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="py-3">
            <div className="flex items-center gap-2 text-yellow-600 text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>No tracks selected. This question will not appear in any track-filtered views.</span>
            </div>
          </CardContent>
        </Card>
      )}

      {selectedTracks.length > 0 && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="py-3">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-green-600 text-sm font-medium">
                <Users className="h-4 w-4" />
                <span>Selected Tracks ({selectedTracks.length})</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {selectedTracks.map(trackId => {
                  const track = tracks.find(t => t.id === trackId);
                  return track ? (
                    <Badge key={trackId} variant="default" className="text-xs">
                      {track.code}: {track.name}
                    </Badge>
                  ) : null;
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
