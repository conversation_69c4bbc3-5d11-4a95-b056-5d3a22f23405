import type { Meta, StoryObj } from "@storybook/nextjs";
import VerifyAccountPage from "./VerifyAccountPage";

const meta: Meta<typeof VerifyAccountPage> = {
  title: "Pages/VerifyAccountPage",
  component: VerifyAccountPage,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const MobileView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
};

export const TabletView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
  },
};

export const ResendingState: Story = {
  args: {},
  play: async ({ canvasElement }) => {
    const canvas = canvasElement;
    const resendButton = canvas.querySelector("button") as HTMLButtonElement;
    if (resendButton && resendButton.textContent?.includes("Resend Code")) {
      resendButton.click();
    }
  },
};
