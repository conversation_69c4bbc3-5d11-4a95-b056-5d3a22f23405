import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import VerifyAccountPage from "./VerifyAccountPage";

// Mock the OTPInput component
jest.mock("../shared/OTPInput/OTPInput", () => {
  return function MockOTPInput({
    length,
    onChange,
    onComplete,
    autoFocus,
  }: {
    length: number;
    onChange: (value: string) => void;
    onComplete?: (value: string) => void;
    autoFocus?: boolean;
  }) {
    return (
      <div data-testid="otp-input">
        <input
          data-testid="otp-field"
          onChange={(e) => onChange(e.target.value)}
          onBlur={() => onComplete && onComplete("28996")}
          placeholder={`Enter ${length} digit code`}
          autoFocus={autoFocus}
        />
      </div>
    );
  };
});

describe("VerifyAccountPage", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component", () => {
    render(<VerifyAccountPage />);
    expect(screen.getByText("Verify Your Account")).toBeInTheDocument();
  });

  it("renders the title", () => {
    render(<VerifyAccountPage />);
    expect(
      screen.getByRole("heading", {
        name: "Verify Your Account Decorative line",
      })
    ).toBeInTheDocument();
  });

  it("renders the instruction text", () => {
    render(<VerifyAccountPage />);
    expect(screen.getByText("We've sent you a passcode.")).toBeInTheDocument();
    expect(
      screen.getByText("Please check your inbox at p*****@g***.com.")
    ).toBeInTheDocument();
  });

  it("renders the OTP input component", () => {
    render(<VerifyAccountPage />);
    expect(screen.getByTestId("otp-input")).toBeInTheDocument();
  });

  it("renders the resend code button", () => {
    render(<VerifyAccountPage />);
    expect(screen.getByText("Resend Code")).toBeInTheDocument();
  });

  it("renders the continue to create password button", () => {
    render(<VerifyAccountPage />);
    expect(screen.getByText("Continue to Create Password")).toBeInTheDocument();
  });

  it("disables continue button initially", () => {
    render(<VerifyAccountPage />);
    const button = screen.getByText("Continue to Create Password");
    expect(button).toBeDisabled();
  });

  it("handles OTP input change", () => {
    render(<VerifyAccountPage />);
    const otpField = screen.getByTestId("otp-field");

    fireEvent.change(otpField, { target: { value: "28996" } });

    expect(otpField).toHaveValue("28996");
  });

  it("handles resend code click", async () => {
    const consoleSpy = jest.spyOn(console, "log").mockImplementation(() => {});
    render(<VerifyAccountPage />);

    const resendButton = screen.getByText("Resend Code");

    fireEvent.click(resendButton);

    expect(screen.getByText("Resending...")).toBeInTheDocument();

    await waitFor(
      () => {
        expect(screen.getByText("Resend Code")).toBeInTheDocument();
      },
      { timeout: 3000 }
    );

    expect(consoleSpy).toHaveBeenCalledWith("Code resent");
    consoleSpy.mockRestore();
  });

  it("disables resend button while resending", async () => {
    render(<VerifyAccountPage />);

    const resendButton = screen.getByText("Resend Code");

    fireEvent.click(resendButton);

    const resendingButton = screen.getByText("Resending...");
    expect(resendingButton).toBeDisabled();
  });

  it("handles continue button click when OTP is complete", () => {
    const consoleSpy = jest.spyOn(console, "log").mockImplementation(() => {});
    render(<VerifyAccountPage />);

    // Simulate OTP completion
    const otpField = screen.getByTestId("otp-field");
    fireEvent.change(otpField, { target: { value: "28996" } });
    fireEvent.blur(otpField); // Triggers onComplete

    expect(consoleSpy).toHaveBeenCalledWith("OTP completed:", "28996");
    consoleSpy.mockRestore();
  });

  it("renders Header with help state", () => {
    render(<VerifyAccountPage />);
    expect(screen.getByRole("banner")).toBeInTheDocument();
    // The header should show "Having Trouble? Get Help" instead of "Have an account? Login"
    expect(screen.getByText("Having Trouble?")).toBeInTheDocument();
  });

  it("renders Footer component", () => {
    render(<VerifyAccountPage />);
    expect(screen.getByRole("contentinfo")).toBeInTheDocument();
  });

  it("has proper layout structure", () => {
    const { container } = render(<VerifyAccountPage />);

    expect(container.firstChild).toHaveClass("min-h-screen");
    expect(container.firstChild).toHaveClass("flex");
    expect(container.firstChild).toHaveClass("flex-col");
  });

  it("displays masked email address", () => {
    render(<VerifyAccountPage />);
    expect(
      screen.getByText("Please check your inbox at p*****@g***.com.")
    ).toBeInTheDocument();
  });

  it("has centered layout", () => {
    render(<VerifyAccountPage />);
    const instructions = screen
      .getByText("We've sent you a passcode.")
      .closest(".space-y-2");
    expect(instructions).toBeInTheDocument();
  });

  it("has proper button styling", () => {
    render(<VerifyAccountPage />);
    const button = screen.getByText("Continue to Create Password");
    expect(button).toHaveClass("w-full");
  });

  it("handles OTP input focus", () => {
    render(<VerifyAccountPage />);
    const otpField = screen.getByTestId("otp-field");
    expect(otpField).toHaveFocus();
  });

  it("maintains state across interactions", () => {
    render(<VerifyAccountPage />);

    const otpField = screen.getByTestId("otp-field");
    const resendButton = screen.getByText("Resend Code");

    // Change OTP
    fireEvent.change(otpField, { target: { value: "12345" } });
    expect(otpField).toHaveValue("12345");

    // Click resend
    fireEvent.click(resendButton);
    expect(screen.getByText("Resending...")).toBeInTheDocument();

    // OTP should still be there
    expect(otpField).toHaveValue("12345");
  });

  it("has proper accessibility attributes", () => {
    render(<VerifyAccountPage />);

    const resendButton = screen.getByText("Resend Code");
    const continueButton = screen.getByText("Continue to Create Password");

    expect(resendButton).toHaveAttribute("type", "button");
    expect(continueButton).toHaveAttribute("type", "button");
  });

  it("shows proper button states", () => {
    render(<VerifyAccountPage />);

    const resendButton = screen.getByText("Resend Code");
    const continueButton = screen.getByText("Continue to Create Password");

    expect(resendButton).not.toBeDisabled();
    expect(continueButton).toBeDisabled();
  });

  it("has proper spacing and layout", () => {
    render(<VerifyAccountPage />);

    const mainContent = screen
      .getByText("We've sent you a passcode.")
      .closest(".space-y-8");
    expect(mainContent).toBeInTheDocument();
    expect(mainContent).toHaveClass("text-center");
  });

  it("has proper title styling", () => {
    render(<VerifyAccountPage />);

    const title = screen.getByRole("heading", {
      name: "Verify Your Account Decorative line",
    });
    expect(title).toHaveClass("font-bold");
  });

  it("handles component mounting and unmounting", () => {
    const { unmount } = render(<VerifyAccountPage />);

    expect(screen.getByText("Verify Your Account")).toBeInTheDocument();

    unmount();

    expect(screen.queryByText("Verify Your Account")).not.toBeInTheDocument();
  });

  it("renders without step header", () => {
    render(<VerifyAccountPage />);

    // Should not have step indicators
    expect(screen.queryByText(/Step \d+ of \d+/)).not.toBeInTheDocument();
  });

  it("uses correct email mask", () => {
    render(<VerifyAccountPage />);

    // Should show p*****@g***.com instead of j*****@g***.com
    expect(
      screen.getByText("Please check your inbox at p*****@g***.com.")
    ).toBeInTheDocument();
    expect(
      screen.queryByText(/j\*\*\*\*\*@g\*\*\*\.com/)
    ).not.toBeInTheDocument();
  });
});
