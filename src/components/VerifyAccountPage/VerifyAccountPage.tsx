import React, { useState } from "react";
import Header, { HeaderState } from "../shared/Header/Header";
import Footer from "../shared/Footer/Footer";
import Button, { ButtonType } from "../shared/Button/Button";
import OTPInput from "../shared/OTPInput/OTPInput";
import PageHeader from "../shared/PageHeader/PageHeader";

const VerifyAccountPage = () => {
  const [otp, setOtp] = useState("");
  const [isResending, setIsResending] = useState(false);

  const handleOTPChange = (otpValue: string) => {
    setOtp(otpValue);
  };

  const handleOTPComplete = (otpValue: string) => {
    console.log("OTP completed:", otpValue);
  };

  const handleResendCode = () => {
    setIsResending(true);
    // Simulate resend API call
    setTimeout(() => {
      setIsResending(false);
      console.log("Code resent");
    }, 2000);
  };

  const handleContinueToCreatePassword = () => {
    if (otp.length === 5) {
      console.log("Continue to Create Password clicked with OTP:", otp);
    }
  };

  const maskedEmail = "p*****@g***.com";

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.HELP} />
      <main className="flex-1 flex justify-center items-center py-4 px-4 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[40.125rem] w-full">
          <div className="bg-white rounded-lg">
            <div className="max-w-md mx-auto space-y-8 text-center">
              {/* Title */}
              <div className="mb-8">
                <PageHeader title="Verify Your Account" />
              </div>

              {/* Instructions */}
              <div className="space-y-2">
                <p className="text-[var(--grey-6)] text-base">
                  We&apos;ve sent you a passcode.
                </p>
                <p className="text-[var(--grey-6)] text-base">
                  Please check your inbox at {maskedEmail}.
                </p>
              </div>

              {/* OTP Input */}
              <div className="flex justify-center py-4">
                <OTPInput
                  length={5}
                  onChange={handleOTPChange}
                  onComplete={handleOTPComplete}
                  autoFocus={true}
                />
              </div>

              {/* Resend Code Link */}
              <div className="py-2">
                <button
                  type="button"
                  onClick={handleResendCode}
                  disabled={isResending}
                  className="text-[var(--grey-5)] text-base hover:text-[var(--grey-6)] transition-colors duration-200 underline disabled:opacity-50"
                >
                  {isResending ? "Resending..." : "Resend Code"}
                </button>
              </div>

              {/* Continue to Create Password Button */}
              <div className="pt-4">
                <Button
                  type={ButtonType.PRIMARY}
                  text="Continue to Create Password"
                  onClick={handleContinueToCreatePassword}
                  disabled={otp.length !== 5}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default VerifyAccountPage;
