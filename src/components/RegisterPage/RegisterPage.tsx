import React, { useState } from "react";
import Header, { HeaderState } from "../shared/Header/Header";
import Footer from "../shared/Footer/Footer";
import Button, { ButtonType } from "../shared/Button/Button";
import ToggleButton from "../shared/ToggleButton/ToggleButton";
import Input from "../shared/Input/Input";
import PageHeader from "../shared/PageHeader/PageHeader";

export interface RegisterPageProps {
  onRegister?: (data: RegistrationData) => void;
  onLoginClick?: () => void;
  className?: string;
}

export interface RegistrationData {
  fullName: string;
  email: string;
  phoneNumber: string;
  age: string;
  sex: "female" | "male";
  dateOfBirth: string;
  acceptsTerms: boolean;
}

const RegisterPage: React.FC<RegisterPageProps> = ({
  onRegister,
  onLoginClick,
  className = "",
}) => {
  const [formData, setFormData] = useState<RegistrationData>({
    fullName: "",
    email: "",
    phoneNumber: "",
    age: "",
    sex: "female",
    dateOfBirth: "",
    acceptsTerms: false,
  });

  const handleInputChange = (
    field: keyof RegistrationData,
    value: string | boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = () => {
    if (onRegister) {
      onRegister(formData);
    }
  };

  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      <Header state={HeaderState.HELP} />

      <main className="flex-1 flex justify-center py-8 px-[1.063rem] md:py-12 md:px-[9.375rem]">
        <div className="max-w-[26.5rem] w-full">
          <div className="text-center mb-8">
            <PageHeader title="Create Your GIVF Account" />
            <p className="text-[var(--grey-6)] text-base font-medium leading-relaxed">
              Let&apos;s begin your journey with GIVF. Create your secure
              account to access your IVF tools and progress.
            </p>
          </div>

          {/* Registration Form */}
          <form className="space-y-6">
            {/* Full Name */}
            <Input
              type="text"
              label="Full Name"
              value={formData.fullName}
              onChange={(value) => handleInputChange("fullName", value)}
              placeholder="Enter your full name"
            />

            {/* Email */}
            <Input
              type="email"
              label="Email"
              value={formData.email}
              onChange={(value) => handleInputChange("email", value)}
              placeholder="Enter your email address"
            />

            {/* Phone Number */}
            <div>
              <label className="block text-[var(--grey-6)] text-base font-medium mb-2">
                Number{" "}
                <span className="text-[var(--grey-5)] font-normal">
                  (Optional)
                </span>
              </label>
              <Input
                type="tel"
                value={formData.phoneNumber}
                onChange={(value) => handleInputChange("phoneNumber", value)}
                placeholder="Enter your phone number"
              />
            </div>

            {/* Age */}
            <Input
              type="number"
              label="Age"
              value={formData.age}
              onChange={(value) => handleInputChange("age", value)}
              placeholder="Enter your age"
            />

            {/* Sex */}
            <div>
              <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                Sex
              </label>
              <div className="flex gap-4">
                <ToggleButton
                  variant="compact"
                  isSelected={formData.sex === "female"}
                  onClick={() => handleInputChange("sex", "female")}
                >
                  Female
                </ToggleButton>
                <ToggleButton
                  variant="compact"
                  isSelected={formData.sex === "male"}
                  onClick={() => handleInputChange("sex", "male")}
                >
                  Male
                </ToggleButton>
              </div>
            </div>

            {/* Date of Birth */}
            <Input
              type="date"
              label="Date of Birth"
              value={formData.dateOfBirth}
              onChange={(value) => handleInputChange("dateOfBirth", value)}
            />

            {/* Terms Acceptance */}
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                id="terms"
                checked={formData.acceptsTerms}
                onChange={(e) =>
                  handleInputChange("acceptsTerms", e.target.checked)
                }
                className="mt-0.5 h-4 w-4 text-[var(--violet-6)] focus:ring-[var(--violet-6)] border-[var(--grey-3)] rounded"
              />
              <label
                htmlFor="terms"
                className="text-[var(--grey-6)] text-base font-medium"
              >
                I accept the{" "}
                <a
                  href="/terms-conditions"
                  className="text-[var(--violet-6)] hover:text-[var(--violet-7)] underline"
                >
                  Terms
                </a>{" "}
                &{" "}
                <a
                  href="/privacy-policy"
                  className="text-[var(--violet-6)] hover:text-[var(--violet-7)] underline"
                >
                  Privacy Policy
                </a>
              </label>
            </div>

            {/* Submit Button */}
            <div className="pt-2">
              <Button
                type={ButtonType.PRIMARY}
                text="Continue to Create Password"
                onClick={handleSubmit}
                disabled={!formData.acceptsTerms}
                className="h-[3.125rem]"
              />
            </div>
          </form>

          {/* Divider */}
          <div className="flex items-center my-6">
            <div className="flex-1 border-t border-[var(--grey-3)]"></div>
            <span className="px-3 text-[var(--grey-5)] text-base">or</span>
            <div className="flex-1 border-t border-[var(--grey-3)]"></div>
          </div>

          {/* Login Link */}
          <div className="text-center">
            <span className="text-[var(--grey-6)] text-base">
              Already have an account?{" "}
              <button
                type="button"
                onClick={onLoginClick}
                className="text-[var(--violet-6)] hover:text-[var(--violet-7)] font-medium underline"
              >
                Login
              </button>
            </span>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default RegisterPage;
