import React, { useState } from "react";
import ProgressBar from "./ProgressBar";

export default {
  title: "Components/Shared/ProgressBar",
  component: ProgressBar,
  argTypes: {
    valueType: {
      control: { type: "select" },
      options: ["number", "emoji"],
    },
    currentValue: {
      control: { type: "number", min: 0, max: 10, step: 1 },
    },
    min: {
      control: { type: "number" },
    },
    max: {
      control: { type: "number" },
    },
  },
};

export const StressLevel = () => {
  const [value, setValue] = useState(4);

  return (
    <div className="p-6 max-w-md">
      <ProgressBar
        label="Stress Level"
        valueType="number"
        startValue={0}
        endValue={10}
        currentValue={value}
        min={0}
        max={10}
        onChange={setValue}
      />
    </div>
  );
};

export const StressLevelEmoji = () => {
  const [value, setValue] = useState(4);

  // Map numeric value to stress emoji
  const getStressEmoji = (val: number) => {
    if (val <= 2) return "😌"; // Very relaxed
    if (val <= 4) return "🙂"; // Calm
    if (val <= 6) return "😐"; // Neutral
    if (val <= 8) return "😟"; // Stressed
    return "😰"; // Very stressed
  };

  return (
    <div className="p-6 max-w-md">
      <ProgressBar
        label="Stress Level"
        valueType="emoji"
        startValue="😌"
        endValue="😰"
        currentValue={value}
        displayValue={getStressEmoji(value)}
        min={0}
        max={10}
        onChange={setValue}
      />
      <p className="mt-4 text-sm text-gray-600">
        Current value: {value} → {getStressEmoji(value)}
      </p>
    </div>
  );
};

export const SleepQuality = () => {
  const [value, setValue] = useState(6);

  // Map numeric value to sleep quality emoji
  const getSleepEmoji = (val: number) => {
    if (val <= 2) return "😴"; // Very tired
    if (val <= 4) return "😑"; // Tired
    if (val <= 6) return "🙂"; // Okay
    if (val <= 8) return "😊"; // Good
    return "😃"; // Excellent
  };

  return (
    <div className="p-6 max-w-md">
      <ProgressBar
        label="Sleep Quality"
        valueType="emoji"
        startValue="😴"
        endValue="😃"
        currentValue={value}
        displayValue={getSleepEmoji(value)}
        min={0}
        max={10}
        onChange={setValue}
      />
      <p className="mt-4 text-sm text-gray-600">
        Current value: {value} → {getSleepEmoji(value)}
      </p>
    </div>
  );
};

export const CustomRange = () => {
  const [value, setValue] = useState(50);

  return (
    <div className="p-6 max-w-md">
      <ProgressBar
        label="Custom Range (0-100)"
        valueType="number"
        startValue={0}
        endValue={100}
        currentValue={value}
        min={0}
        max={100}
        onChange={setValue}
      />
    </div>
  );
};

export const MoodTracker = () => {
  const [value, setValue] = useState(3);
  const moodEmojis = ["😢", "😕", "😐", "🙂", "😊"];

  return (
    <div className="p-6 max-w-md">
      <ProgressBar
        label="Mood Tracker"
        valueType="emoji"
        startValue="😢"
        endValue="😊"
        currentValue={value}
        displayValue={moodEmojis[value] || "😐"}
        min={0}
        max={4}
        onChange={setValue}
      />
      <p className="mt-4 text-sm text-gray-600">
        Current value: {value} → {moodEmojis[value] || "😐"}
      </p>
    </div>
  );
};

export const Disabled = () => {
  return (
    <div className="p-6 max-w-md">
      <ProgressBar
        label="Read-only Progress"
        valueType="number"
        startValue={0}
        endValue={10}
        currentValue={7}
        min={0}
        max={10}
      />
    </div>
  );
};

export const Interactive = {
  args: {
    label: "Interactive Progress Bar",
    valueType: "number",
    startValue: 0,
    endValue: 10,
    currentValue: 5,
    min: 0,
    max: 10,
  },

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  render: (args: any) => {
    const [value, setValue] = useState(args.currentValue);

    return (
      <div className="p-6 max-w-md">
        <ProgressBar {...args} currentValue={value} onChange={setValue} />
        <p className="mt-4 text-sm text-gray-600">Current value: {value}</p>
      </div>
    );
  },
};
