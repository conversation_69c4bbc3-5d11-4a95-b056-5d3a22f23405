import React from "react";
import { render, screen } from "@testing-library/react";
import StepHeader from "./StepHeader";

describe("StepHeader", () => {
  const defaultProps = {
    currentStep: 3,
    totalSteps: 5,
    title: "Test Title",
  };

  it("renders step indicator correctly", () => {
    render(<StepHeader {...defaultProps} />);

    expect(screen.getByText(/Step/)).toBeInTheDocument();
    expect(screen.getByText("3")).toBeInTheDocument();
    expect(screen.getByText(/of/)).toBeInTheDocument();
    expect(screen.getByText("5")).toBeInTheDocument();
  });

  it("renders title correctly", () => {
    render(<StepHeader {...defaultProps} />);

    expect(screen.getByText("Test Title")).toBeInTheDocument();
  });

  it("renders decorative line image", () => {
    render(<StepHeader {...defaultProps} />);

    const decorativeLine = screen.getByAltText("Decorative line");
    expect(decorativeLine).toBeInTheDocument();
    expect(decorativeLine).toHaveAttribute("src", "/assets/loginPage/Line.png");
  });

  it("applies custom className", () => {
    const { container } = render(
      <StepHeader {...defaultProps} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("renders different step numbers", () => {
    render(<StepHeader currentStep={1} totalSteps={10} title="Test" />);

    expect(screen.getByText("1")).toBeInTheDocument();
    expect(screen.getByText("10")).toBeInTheDocument();
  });

  it("renders long titles correctly", () => {
    const longTitle =
      "This is a very long title that should still render correctly";
    render(<StepHeader {...defaultProps} title={longTitle} />);

    expect(screen.getByText(longTitle)).toBeInTheDocument();
  });

  it("has correct HTML structure", () => {
    render(<StepHeader {...defaultProps} />);

    const stepParagraph = screen.getByText(/Step/);
    expect(stepParagraph).toHaveClass("text-[var(--violet-6)]");

    const titleHeading = screen.getByRole("heading", { level: 1 });
    expect(titleHeading).toHaveClass("text-[var(--grey-7)]");
  });

  it("has proper accessibility attributes", () => {
    render(<StepHeader {...defaultProps} />);

    const heading = screen.getByRole("heading", { level: 1 });
    expect(heading).toBeInTheDocument();
    expect(heading).toHaveTextContent("Test Title");
  });
});
