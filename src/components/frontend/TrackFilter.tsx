"use client";

import React, { useState, useEffect } from "react";
import { Track } from "@/types/fertility-questions";
import { But<PERSON> } from "@/components/ShadcnUI/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ShadcnUI/radio-group";
import { Label } from "@/components/ShadcnUI/label";
import { Target, Users, CheckCircle } from "lucide-react";
import { getAllTracks } from "@/lib/services/tracks.service";

interface TrackFilterProps {
  selectedTrack?: string;
  onTrackSelect: (trackId: string | undefined) => void;
  showAllOption?: boolean;
}

export function TrackFilter({
  selectedTrack,
  onTrackSelect,
  showAllOption = true
}: TrackFilterProps) {
  const [tracks, setTracks] = useState<Track[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTracks = async () => {
      try {
        setLoading(true);
        const fetchedTracks = await getAllTracks();
        setTracks(fetchedTracks);
        setError(null);
      } catch (err) {
        console.error("Error fetching tracks:", err);
        setError("Failed to load tracks");
      } finally {
        setLoading(false);
      }
    };

    fetchTracks();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Select Your Track
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-sm text-muted-foreground">Loading tracks...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardContent className="py-4">
          <div className="flex items-center gap-2 text-red-600 text-sm">
            <Target className="h-4 w-4" />
            <span>{error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          Select Your Track
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Choose the track that best describes your fertility journey to see relevant questions.
        </p>
      </CardHeader>
      <CardContent>
        <RadioGroup
          value={selectedTrack || "all"}
          onValueChange={(value) => onTrackSelect(value === "all" ? undefined : value)}
          className="space-y-3"
        >
          {showAllOption && (
            <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors">
              <RadioGroupItem value="all" id="all" />
              <Label htmlFor="all" className="flex-1 cursor-pointer">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-primary" />
                    <span className="font-medium">All Questions</span>
                  </div>
                  {!selectedTrack && (
                    <Badge variant="default" className="text-xs">
                      Selected
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Show questions from all tracks
                </p>
              </Label>
            </div>
          )}

          {tracks.map((track) => {
            const isSelected = selectedTrack === track.id;
            
            return (
              <div 
                key={track.id}
                className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                  isSelected ? "border-primary bg-primary/5" : "hover:bg-muted/50"
                }`}
              >
                <RadioGroupItem value={track.id} id={track.id} />
                <Label htmlFor={track.id} className="flex-1 cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span className="font-medium">{track.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {track.code}
                      </Badge>
                    </div>
                    {isSelected && (
                      <Badge variant="default" className="text-xs">
                        Selected
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {track.description}
                  </p>
                </Label>
              </div>
            );
          })}
        </RadioGroup>

        {selectedTrack && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-600 text-sm font-medium">
              <CheckCircle className="h-4 w-4" />
              <span>Track Selected</span>
            </div>
            <p className="text-sm text-green-600 mt-1">
              You'll see questions tailored for{" "}
              {tracks.find(t => t.id === selectedTrack)?.name || "your selected track"}.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Compact version for inline use
export function CompactTrackFilter({
  selectedTrack,
  onTrackSelect,
  showAllOption = true
}: TrackFilterProps) {
  const [tracks, setTracks] = useState<Track[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTracks = async () => {
      try {
        const fetchedTracks = await getAllTracks();
        setTracks(fetchedTracks);
      } catch (err) {
        console.error("Error fetching tracks:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchTracks();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center gap-2">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
        <span className="text-sm text-muted-foreground">Loading tracks...</span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2 flex-wrap">
      <span className="text-sm font-medium">Track:</span>
      
      {showAllOption && (
        <Button
          variant={!selectedTrack ? "default" : "outline"}
          size="sm"
          onClick={() => onTrackSelect(undefined)}
        >
          All
        </Button>
      )}

      {tracks.map((track) => (
        <Button
          key={track.id}
          variant={selectedTrack === track.id ? "default" : "outline"}
          size="sm"
          onClick={() => onTrackSelect(track.id)}
        >
          {track.code}
        </Button>
      ))}
    </div>
  );
}
