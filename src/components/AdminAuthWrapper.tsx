"use client";

import React from "react";
import { useAdminAuth } from "@/hooks/useAdminAuth";
import PageLoader from "@/components/shared/PageLoader";

interface AdminAuthWrapperProps {
  children: React.ReactNode;
}

export default function AdminAuthWrapper({ children }: AdminAuthWrapperProps) {
  const { isAuthorized, loading } = useAdminAuth();

  // Show loading state while checking permissions
  if (loading) {
    return <PageLoader />;
  }

  // If not authorized, the useAdminAuth hook will handle the redirect
  // This component will only render if authorized
  if (!isAuthorized) {
    return null;
  }

  return <>{children}</>;
} 