import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import CreatePasswordPage from "./CreatePasswordPage";

// Mock the PasswordInput component
jest.mock("../shared/PasswordInput/PasswordInput", () => {
  return function MockPasswordInput({
    id,
    value,
    onChange,
    placeholder,
  }: {
    id: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    placeholder: string;
  }) {
    return (
      <input
        id={id}
        data-testid={id}
        type="password"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
      />
    );
  };
});

describe("CreatePasswordPage", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component", () => {
    render(<CreatePasswordPage />);
    expect(screen.getByText("Create Password")).toBeInTheDocument();
  });

  it("renders the page header", () => {
    render(<CreatePasswordPage />);
    expect(
      screen.getByRole("heading", { name: "Create Password Decorative line" })
    ).toBeInTheDocument();
  });

  it("renders the description text", () => {
    render(<CreatePasswordPage />);
    expect(
      screen.getByText(
        "Choose your password to begin, this will allow you to login again into your IVF account"
      )
    ).toBeInTheDocument();
  });

  it("renders both password input fields", () => {
    render(<CreatePasswordPage />);
    expect(screen.getByLabelText("New Password")).toBeInTheDocument();
    expect(screen.getByLabelText("Confirm Password")).toBeInTheDocument();
  });

  it("renders password requirements", () => {
    render(<CreatePasswordPage />);
    expect(screen.getByText("At least 8 character")).toBeInTheDocument();
    expect(screen.getByText("1 Lowercase letter")).toBeInTheDocument();
    expect(screen.getByText("1 Upper case letter")).toBeInTheDocument();
    expect(screen.getByText("1 A symbol (@&%$)")).toBeInTheDocument();
  });

  it("renders the continue button", () => {
    render(<CreatePasswordPage />);
    expect(screen.getByText("Continue to Verify Email")).toBeInTheDocument();
  });

  it("disables continue button initially", () => {
    render(<CreatePasswordPage />);
    const button = screen.getByText("Continue to Verify Email");
    expect(button).toBeDisabled();
  });

  it("handles new password input change", () => {
    render(<CreatePasswordPage />);
    const newPasswordInput = screen.getByTestId("new-password");

    fireEvent.change(newPasswordInput, { target: { value: "TestPass123!" } });

    expect(newPasswordInput).toHaveValue("TestPass123!");
  });

  it("handles confirm password input change", () => {
    render(<CreatePasswordPage />);
    const confirmPasswordInput = screen.getByTestId("confirm-password");

    fireEvent.change(confirmPasswordInput, {
      target: { value: "TestPass123!" },
    });

    expect(confirmPasswordInput).toHaveValue("TestPass123!");
  });

  it("validates password length requirement", () => {
    render(<CreatePasswordPage />);
    const newPasswordInput = screen.getByTestId("new-password");

    // Should be checked after 8 characters
    fireEvent.change(newPasswordInput, { target: { value: "12345678" } });
    expect(screen.getByText("At least 8 character")).toBeInTheDocument();
  });

  it("validates lowercase letter requirement", () => {
    render(<CreatePasswordPage />);
    const newPasswordInput = screen.getByTestId("new-password");

    fireEvent.change(newPasswordInput, { target: { value: "PASSWORD123!" } });
    // No lowercase - should be unchecked

    fireEvent.change(newPasswordInput, { target: { value: "Password123!" } });
    // Has lowercase - should be checked

    expect(screen.getByText("1 Lowercase letter")).toBeInTheDocument();
  });

  it("validates uppercase letter requirement", () => {
    render(<CreatePasswordPage />);
    const newPasswordInput = screen.getByTestId("new-password");

    fireEvent.change(newPasswordInput, { target: { value: "password123!" } });
    // No uppercase - should be unchecked

    fireEvent.change(newPasswordInput, { target: { value: "Password123!" } });
    // Has uppercase - should be checked

    expect(screen.getByText("1 Upper case letter")).toBeInTheDocument();
  });

  it("validates symbol requirement", () => {
    render(<CreatePasswordPage />);
    const newPasswordInput = screen.getByTestId("new-password");

    fireEvent.change(newPasswordInput, { target: { value: "Password123" } });
    // No symbol - should be unchecked

    fireEvent.change(newPasswordInput, { target: { value: "Password123!" } });
    // Has symbol - should be checked

    expect(screen.getByText("1 A symbol (@&%$)")).toBeInTheDocument();
  });

  it("enables continue button when all requirements are met and passwords match", () => {
    render(<CreatePasswordPage />);
    const newPasswordInput = screen.getByTestId("new-password");
    const confirmPasswordInput = screen.getByTestId("confirm-password");
    const continueButton = screen.getByText("Continue to Verify Email");

    // Enter valid password that meets all requirements
    fireEvent.change(newPasswordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, {
      target: { value: "Password123!" },
    });

    expect(continueButton).not.toBeDisabled();
  });

  it("keeps continue button disabled when passwords don't match", () => {
    render(<CreatePasswordPage />);
    const newPasswordInput = screen.getByTestId("new-password");
    const confirmPasswordInput = screen.getByTestId("confirm-password");
    const continueButton = screen.getByText("Continue to Verify Email");

    fireEvent.change(newPasswordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, {
      target: { value: "DifferentPass!" },
    });

    expect(continueButton).toBeDisabled();
  });

  it("handles continue button click when enabled", () => {
    const consoleSpy = jest.spyOn(console, "log").mockImplementation(() => {});
    render(<CreatePasswordPage />);

    const newPasswordInput = screen.getByTestId("new-password");
    const confirmPasswordInput = screen.getByTestId("confirm-password");
    const continueButton = screen.getByText("Continue to Verify Email");

    // Make form valid
    fireEvent.change(newPasswordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, {
      target: { value: "Password123!" },
    });

    fireEvent.click(continueButton);

    expect(consoleSpy).toHaveBeenCalledWith("Continue to Verify Email clicked");
    consoleSpy.mockRestore();
  });

  it("renders login link", () => {
    render(<CreatePasswordPage />);
    expect(screen.getByText("Already have an account?")).toBeInTheDocument();
    expect(screen.getByText("Login")).toBeInTheDocument();
  });

  it("handles login link click", () => {
    const consoleSpy = jest.spyOn(console, "log").mockImplementation(() => {});
    render(<CreatePasswordPage />);

    const loginLink = screen.getByText("Login");
    fireEvent.click(loginLink);

    expect(consoleSpy).toHaveBeenCalledWith("Navigate to login");
    consoleSpy.mockRestore();
  });

  it("renders Header with help state", () => {
    render(<CreatePasswordPage />);
    expect(screen.getByRole("banner")).toBeInTheDocument();
    expect(screen.getByText("Having Trouble?")).toBeInTheDocument();
  });

  it("renders Footer component", () => {
    render(<CreatePasswordPage />);
    expect(screen.getByRole("contentinfo")).toBeInTheDocument();
  });

  it("has proper layout structure", () => {
    const { container } = render(<CreatePasswordPage />);

    expect(container.firstChild).toHaveClass("min-h-screen");
    expect(container.firstChild).toHaveClass("flex");
    expect(container.firstChild).toHaveClass("flex-col");
  });

  it("has proper form labels", () => {
    render(<CreatePasswordPage />);

    expect(screen.getByLabelText("New Password")).toBeInTheDocument();
    expect(screen.getByLabelText("Confirm Password")).toBeInTheDocument();
  });

  it("renders requirement helper text", () => {
    render(<CreatePasswordPage />);
    expect(
      screen.getByText("Must have at least 8 character")
    ).toBeInTheDocument();
    expect(
      screen.getByText("To make your password Stronger:")
    ).toBeInTheDocument();
  });

  it("renders or separator", () => {
    render(<CreatePasswordPage />);
    expect(screen.getByText("or")).toBeInTheDocument();
  });

  it("has proper accessibility attributes", () => {
    render(<CreatePasswordPage />);

    const newPasswordInput = screen.getByTestId("new-password");
    const confirmPasswordInput = screen.getByTestId("confirm-password");
    const loginButton = screen.getByText("Login");

    expect(newPasswordInput).toHaveAttribute("type", "password");
    expect(confirmPasswordInput).toHaveAttribute("type", "password");
    expect(loginButton).toHaveAttribute("type", "button");
  });

  it("maintains state across multiple password changes", () => {
    render(<CreatePasswordPage />);
    const newPasswordInput = screen.getByTestId("new-password");

    // Test multiple password changes
    fireEvent.change(newPasswordInput, { target: { value: "short" } });
    expect(newPasswordInput).toHaveValue("short");

    fireEvent.change(newPasswordInput, {
      target: { value: "LongerPassword123!" },
    });
    expect(newPasswordInput).toHaveValue("LongerPassword123!");

    fireEvent.change(newPasswordInput, { target: { value: "" } });
    expect(newPasswordInput).toHaveValue("");
  });

  it("handles special symbols in password validation", () => {
    render(<CreatePasswordPage />);
    const newPasswordInput = screen.getByTestId("new-password");

    // Test different symbols
    fireEvent.change(newPasswordInput, { target: { value: "Password123&" } });
    expect(screen.getByText("1 A symbol (@&%$)")).toBeInTheDocument();

    fireEvent.change(newPasswordInput, { target: { value: "Password123%" } });
    expect(screen.getByText("1 A symbol (@&%$)")).toBeInTheDocument();

    fireEvent.change(newPasswordInput, { target: { value: "Password123$" } });
    expect(screen.getByText("1 A symbol (@&%$)")).toBeInTheDocument();
  });
});
