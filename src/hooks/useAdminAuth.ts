"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "./useAuth";
import { useUserCan } from "./useUserCan";

export function useAdminAuth() {
  const { user, loading: authLoading } = useAuth();
  const { data: isAuthorized, isLoading: permissionLoading } = useUserCan('admin.access');
  const router = useRouter();

  useEffect(() => {
    // If user is not authenticated, redirect to login
    if (!authLoading && !user) {
      router.push("/login");
      return;
    }

    // If permission check is complete and user is not authorized, redirect to login
    if (!permissionLoading && !authLoading && user && !isAuthorized) {
      router.push("/login");
    }
  }, [user, authLoading, isAuthorized, permissionLoading, router]);

  return { 
    isAuthorized, 
    loading: authLoading || permissionLoading,
    user 
  };
} 