import { z } from "zod";
import { FormFieldType, ScoringType } from "@/types/fertility-questions";

// Base validation schemas
export const formFieldTypeSchema = z.nativeEnum(FormFieldType);
export const scoringTypeSchema = z.nativeEnum(ScoringType);

export const formBuilderOptionSchema = z.object({
  id: z.string().optional(),
  option_text: z.string().min(1, "Option text is required").max(200, "Option text too long"),
  value: z.string().optional(),
  order: z.number().int().min(0, "Order must be non-negative"),
  score: z.number().min(0, "Score must be non-negative").optional(),
});

// Scoring configuration schemas
export const rangeScoreConfigSchema = z.object({
  min: z.number(),
  max: z.number(),
  score: z.number().min(0, "Score must be non-negative"),
}).refine((data) => data.min < data.max, {
  message: "Min value must be less than max value",
  path: ["min"]
});

export const singleChoiceScoreConfigSchema = z.object({
  option: z.string().min(1, "Option is required"),
  score: z.number().min(0, "Score must be non-negative"),
});

export const scoringConfigSchema = z.union([
  z.array(rangeScoreConfigSchema),
  z.array(singleChoiceScoreConfigSchema)
]);

const baseFormBuilderQuestionSchema = z.object({
  id: z.string().optional(),
  question_text: z.string().min(1, "Question text is required").max(500, "Question text too long"),
  field_type: formFieldTypeSchema,
  placeholder: z.string().max(200, "Placeholder too long").optional(),
  min_value: z.number().int().optional(),
  max_value: z.number().int().optional(),
  step: z.number().int().min(1, "Step must be positive").optional(),
  unit: z.string().max(20, "Unit too long").optional(),
  order: z.number().int().min(0, "Order must be non-negative"),
  options: z.array(formBuilderOptionSchema).optional(),

  // Scoring fields
  scoring_type: scoringTypeSchema.optional(),
  scoring_config: scoringConfigSchema.optional(),
});

export const formBuilderQuestionSchema = baseFormBuilderQuestionSchema.refine((data) => {
  // Validate that min_value is less than max_value if both are provided
  if (data.min_value !== undefined && data.max_value !== undefined) {
    return data.min_value < data.max_value;
  }
  return true;
}, {
  message: "Minimum value must be less than maximum value",
  path: ["min_value"]
}).refine((data) => {
  // Validate that options are provided for select fields
  if (data.field_type === FormFieldType.RADIO_SELECT || data.field_type === FormFieldType.DROPDOWN_SELECT) {
    return data.options && data.options.length >= 2;
  }
  return true;
}, {
  message: "Select fields must have at least 2 options",
  path: ["options"]
}).refine((data) => {
  // Validate that range fields have min and max values
  if (data.field_type === FormFieldType.NUMBER_INPUT || data.field_type === FormFieldType.RANGE_SLIDER) {
    return data.min_value !== undefined && data.max_value !== undefined;
  }
  return true;
}, {
  message: "Range fields must have minimum and maximum values",
  path: ["min_value"]
}).refine((data) => {
  // Validate scoring configuration consistency
  if (data.scoring_type === ScoringType.range) {
    return Array.isArray(data.scoring_config) && data.scoring_config.length > 0;
  }
  return true;
}, {
  message: "Range scoring type requires at least one range configuration",
  path: ["scoring_config"]
}).refine((data) => {
  // Validate single choice scoring has scores for all options
  if (data.scoring_type === ScoringType.single_choice) {
    if (!data.options || data.options.length === 0) {
      return false;
    }
    return data.options.every(option => option.score !== undefined && option.score !== null);
  }
  return true;
}, {
  message: "Single choice scoring requires all options to have scores",
  path: ["options"]
});

export const formBuilderFormSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Form name is required").max(100, "Form name too long"),
  description: z.string().max(500, "Description too long").optional(),
  questions: z.array(formBuilderQuestionSchema),
});

// Server action validation schemas
export const createFormSchema = formBuilderFormSchema.omit({ id: true });

export const updateFormSchema = z.object({
  id: z.string().uuid("Invalid form ID"),
  name: z.string().min(1, "Form name is required").max(100, "Form name too long").optional(),
  description: z.string().max(500, "Description too long").optional(),
});

export const createQuestionSchema = z.object({
  formId: z.string().uuid("Invalid form ID"),
  question: baseFormBuilderQuestionSchema.omit({ id: true }),
});

export const updateQuestionSchema = z.object({
  questionId: z.string().uuid("Invalid question ID"),
  question: baseFormBuilderQuestionSchema.partial().omit({ id: true }),
});

export const deleteQuestionSchema = z.object({
  questionId: z.string().uuid("Invalid question ID"),
});

export const reorderQuestionsSchema = z.object({
  formId: z.string().uuid("Invalid form ID"),
  questionOrders: z.array(z.object({
    id: z.string().uuid("Invalid question ID"),
    order: z.number().int().min(0, "Order must be non-negative"),
  })),
});

export const updateQuestionOptionsSchema = z.object({
  questionId: z.string().uuid("Invalid question ID"),
  options: z.array(formBuilderOptionSchema.omit({ id: true })),
});

// Form data validation for frontend
export const questionFormDataSchema = z.object({
  question_text: z.string().min(1, "Question text is required"),
  field_type: formFieldTypeSchema,
  placeholder: z.string().optional(),
  min_value: z.coerce.number().optional(),
  max_value: z.coerce.number().optional(),
  step: z.coerce.number().min(1).optional(),
  unit: z.string().optional(),
  options: z.array(z.object({
    option_text: z.string().min(1, "Option text is required"),
    value: z.string().optional(),
  })).optional(),
});

// Validation helper functions
export function validateFieldType(fieldType: FormFieldType, data: Record<string, unknown>): string[] {
  const errors: string[] = [];
  const options = Array.isArray(data.options) ? data.options : [];

  switch (fieldType) {
    case FormFieldType.INPUT:
      if (data.min_value !== undefined || data.max_value !== undefined) {
        errors.push("Text input fields cannot have min/max values");
      }
      if (options.length > 0) {
        errors.push("Text input fields cannot have options");
      }
      break;

    case FormFieldType.NUMBER_INPUT:
      if (options.length > 0) {
        errors.push("Number input fields cannot have options");
      }
      break;

    case FormFieldType.RADIO_SELECT:
    case FormFieldType.DROPDOWN_SELECT:
      if (options.length < 2) {
        errors.push("Select fields must have at least 2 options");
      }
      if (data.min_value !== undefined || data.max_value !== undefined) {
        errors.push("Select fields cannot have min/max values");
      }
      if (data.unit) {
        errors.push("Select fields cannot have units");
      }
      break;

    case FormFieldType.RANGE_SLIDER:
      if (options.length > 0) {
        errors.push("Range slider fields cannot have options");
      }
      if (data.min_value === undefined || data.max_value === undefined) {
        errors.push("Range slider fields must have min and max values");
      }
      break;
  }

  return errors;
}

export function sanitizeQuestionData(data: Record<string, unknown>): Record<string, unknown> {
  const sanitized = { ...data };

  // Remove irrelevant fields based on field type
  switch (data.field_type) {
    case FormFieldType.INPUT:
      delete sanitized.min_value;
      delete sanitized.max_value;
      delete sanitized.step;
      delete sanitized.unit;
      delete sanitized.options;
      break;

    case FormFieldType.NUMBER_INPUT:
      delete sanitized.options;
      break;

    case FormFieldType.RADIO_SELECT:
    case FormFieldType.DROPDOWN_SELECT:
      delete sanitized.min_value;
      delete sanitized.max_value;
      delete sanitized.step;
      delete sanitized.unit;
      delete sanitized.placeholder;
      break;

    case FormFieldType.RANGE_SLIDER:
      delete sanitized.options;
      delete sanitized.placeholder;
      break;
  }

  return sanitized;
}
