# IVF Scores Form API

This is a consolidated API endpoint that handles all IVF score form steps in a single endpoint, with category-based differentiation.

## Endpoint

`/api/v1/ivf-scores/form`

## Methods

### GET - Retrieve Form Data

Retrieves IVF form data, optionally filtered by category.

**URL Parameters:**
- `category` (optional): `biological`, `lifestyle`, `environmental`, or `all` (default: `all`)

**Examples:**
```bash
# Get all form data
GET /api/v1/ivf-scores/form

# Get only biological factors data
GET /api/v1/ivf-scores/form?category=biological

# Get only lifestyle & psychosocial data
GET /api/v1/ivf-scores/form?category=lifestyle

# Get only environmental & socioeconomic data
GET /api/v1/ivf-scores/form?category=environmental
```

**Response:**
```json
{
  "success": true,
  "data": {
    "formData": {
      // Step-specific or all data fields
    },
    "stepCompleted": true,
    "isGuest": false,
    "currentStep": 2
  }
}
```

### POST - Create/Update Form Data

Creates or updates IVF form data for a specific step.

**URL Parameters:**
- `category` (required): `biological`, `lifestyle`, or `environmental`

**Request Body:**
The request body should contain the data for the specified form:

**Biological Factors:**
```json
{
  "age": 30,
  "height": 165,
  "weight": 60,
  "menstrual_regularity": "regular",
  "infertility_duration": "under_6_months",
  "ivf_attempts": 0,
  "known_conditions": ""
}
```

**Lifestyle & Psychosocial:**
```json
{
  "stress_level": 5,
  "diet_type": "balanced",
  "exercise_frequency": "two_to_three_times",
  "sleep_quality": 7,
  "emotional_support_at_home": true,
  "smoking_or_alcohol_habits": ""
}
```

**Environmental & Socioeconomic:**
```json
{
  "household_income_range": "from_10k_to_50k",
  "living_area": "urban",
  "work_stress_level": "medium",
  "pollution_exposure": "moderate",
  "occupation_type": "desk_job"
}
```

**Examples:**
```bash
# Save biological factors data
POST /api/v1/ivf-scores/form?category=biological
Content-Type: application/json

{
  "age": 30,
  "height": 165,
  "weight": 60,
  "menstrual_regularity": "regular",
  "infertility_duration": "under_6_months",
  "ivf_attempts": 0,
  "known_conditions": ""
}
```

**Response:**
```json
{
  "success": true,
  "message": "Biological factors data saved successfully",
  "data": {
    "ivfScores": {
      // Complete IVF scores data
    },
    "nextStep": 2,
    "isGuest": false
  }
}
```

### PUT - Update Form Data

Updates existing IVF form data for a specific step.

**URL Parameters:**
- `category` (required): `biological`, `lifestyle`, or `environmental`

**Request Body:**
Partial data for the specified form (same structure as POST but fields are optional).

**Examples:**
```bash
# Update biological factors data
PUT /api/v1/ivf-scores/form?category=biological
Content-Type: application/json

{
  "age": 31,
  "weight": 62
}
```

**Response:**
```json
{
  "success": true,
  "message": "biological data updated successfully",
  "data": {
    "ivfScores": {
      // Complete IVF scores data
    },
    "isGuest": false
  }
}
```

## Form Progression

The API enforces form progression:
- Lifestyle & Psychosocial can only be completed after Biological Factors
- Environmental & Socioeconomic can only be completed after Biological Factors and Lifestyle & Psychosocial
- Attempting to save a form without completing previous forms will return a 400 error

## Guest Users

For guest users:
- A guest session token is returned in the `X-Guest-Session` header
- The response includes `isGuest: true`
- After completing all steps, guests need to verify their email to see results

## Error Handling

Common error responses:

**400 Bad Request:**
- Missing or invalid category parameter
- Invalid data format
- Form progression violation

**404 Not Found:**
- No existing data found (for PUT requests)

**500 Internal Server Error:**
- Database or server errors

## Migration from Individual Endpoints

This consolidated endpoint replaces the following individual endpoints:
- `/api/v1/ivf-scores/biological/route.ts` (Step 1)
- `/api/v1/ivf-scores/lifestyle-psychoscocial/route.ts` (Step 2)
- `/api/v1/ivf-scores/environmental-socioeconomic/route.ts` (Step 3)

**Frontend Migration:**
Instead of making separate calls to different endpoints, use the category parameter:

```javascript
// Old way
const step1Data = await fetch('/api/v1/ivf-scores/biological');
const step2Data = await fetch('/api/v1/ivf-scores/lifestyle-psychoscocial');
const step3Data = await fetch('/api/v1/ivf-scores/environmental-socioeconomic');

// New way
const biologicalData = await fetch('/api/v1/ivf-scores/form?category=biological');
const lifestyleData = await fetch('/api/v1/ivf-scores/form?category=lifestyle');
const environmentalData = await fetch('/api/v1/ivf-scores/form?category=environmental');
const allData = await fetch('/api/v1/ivf-scores/form'); // or ?category=all
``` 