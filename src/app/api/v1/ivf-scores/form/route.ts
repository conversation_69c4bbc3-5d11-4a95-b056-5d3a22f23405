import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";
import { getUserOrGuestContext, getIVFData, saveIVFData } from "@/utils/api/userOrGuest";
import {
  biologicalFactorsSchema,
  lifestylePsychosocialSchema,
  environmentalSocioeconomicSchema,
  updateBiologicalFactorsSchema,
  updateLifestylePsychosocialSchema,
  updateEnvironmentalSocioeconomicSchema,
} from "@/validations/ivf-scores";

const stepNumberToFormName: { [key: string]: string } = {
  1: 'biological',
  2: 'lifestyle',
  3: 'environmental',
};

/**
 * GET /api/v1/ivf-scores/form
 * Retrieve all IVF form data for authenticated users or guests
 * Can optionally filter by category (step1, step2, step3, or all)
 */
export async function GET(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const { searchParams } = new URL(req.url);
    const category = searchParams.get('category'); // 'biological', 'lifestyle', 'environmental', or 'all'

    const { success, data, error: dataError } = await getIVFData(context!);

    if (!success) {
      return apiResponse(500, dataError || "Failed to retrieve data");
    }

    if (!data) {
      return apiResponse(200, undefined, {
        formData: null,
        stepCompleted: false,
        isGuest: !context!.isAuthenticated
      });
    }

    // Extract data based on category
    let formData;
    let stepCompleted = false;

    switch (category) {
      case 'biological':
        formData = {
          id: data.id,
          age: data.age,
          height: data.height,
          weight: data.weight,
          menstrual_regularity: data.menstrual_regularity,
          infertility_duration: data.infertility_duration,
          ivf_attempts: data.ivf_attempts,
          known_conditions: data.known_conditions,
          current_step: data.current_step,
          created_at: data.created_at,
          updated_at: data.updated_at,
        };
        stepCompleted = data.current_step >= 1;
        break;

      case 'lifestyle':
        formData = {
          id: data.id,
          stress_level: data.stress_level,
          diet_type: data.diet_type,
          exercise_frequency: data.exercise_frequency,
          sleep_quality: data.sleep_quality,
          emotional_support_at_home: data.emotional_support_at_home,
          smoking_or_alcohol_habits: data.smoking_or_alcohol_habits,
          current_step: data.current_step,
          created_at: data.created_at,
          updated_at: data.updated_at,
        };
        stepCompleted = data.current_step >= 2;
        break;

      case 'environmental':
        formData = {
          id: data.id,
          household_income_range: data.household_income_range,
          living_area: data.living_area,
          work_stress_level: data.work_stress_level,
          pollution_exposure: data.pollution_exposure,
          occupation_type: data.occupation_type,
          current_step: data.current_step,
          created_at: data.created_at,
          updated_at: data.updated_at,
        };
        stepCompleted = data.current_step >= 3;
        break;

      case 'all':
      default:
        // Return all data
        formData = {
          id: data.id,
          age: data.age,
          height: data.height,
          weight: data.weight,
          menstrual_regularity: data.menstrual_regularity,
          infertility_duration: data.infertility_duration,
          ivf_attempts: data.ivf_attempts,
          known_conditions: data.known_conditions,
          stress_level: data.stress_level,
          diet_type: data.diet_type,
          exercise_frequency: data.exercise_frequency,
          sleep_quality: data.sleep_quality,
          emotional_support_at_home: data.emotional_support_at_home,
          smoking_or_alcohol_habits: data.smoking_or_alcohol_habits,
          household_income_range: data.household_income_range,
          living_area: data.living_area,
          work_stress_level: data.work_stress_level,
          pollution_exposure: data.pollution_exposure,
          occupation_type: data.occupation_type,
          current_step: data.current_step,
          created_at: data.created_at,
          updated_at: data.updated_at,
        };
        stepCompleted = data.current_step >= 3;
        break;
    }

    return apiResponse(200, undefined, {
      formData,
      stepCompleted,
      isGuest: !context!.isAuthenticated,
      currentStep: data.current_step
    });
  } catch (error) {
    console.error("GET form error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-scores/form
 * Create or update IVF form data for authenticated users or guests
 * Requires category parameter to determine which step to save
 */
export async function POST(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const { searchParams } = new URL(req.url);
    const category = searchParams.get('category'); // 'biological', 'lifestyle', 'environmental'

    if (!category || !['biological', 'lifestyle', 'environmental'].includes(category)) {
      return apiResponse(400, "Category parameter is required and must be 'biological', 'lifestyle', or 'environmental'");
    }

    const body = await req.json();
    let result;
    let stepNumber: number;

    // Validate data based on category
    switch (category) {
      case 'biological':
        result = biologicalFactorsSchema.safeParse(body);
        stepNumber = 1;
        break;
      case 'lifestyle':
        result = lifestylePsychosocialSchema.safeParse(body);
        stepNumber = 2;
        break;
      case 'environmental':
        result = environmentalSocioeconomicSchema.safeParse(body);
        stepNumber = 3;
        break;
      default:
        return apiResponse(400, "Invalid category");
    }

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check step progression for steps 2 and 3
    if (stepNumber > 1) {
      const { success: getSuccess, data: existingData } = await getIVFData(context!);

      if (!getSuccess || !existingData) {
        return apiResponse(400, `Previous form(s) must be completed before '${stepNumberToFormName[String(stepNumber)]}' form. Please complete previous forms first.`);
      }

      if (existingData.current_step < stepNumber - 1) {
        return apiResponse(400, `Previous form(s) must be completed before '${stepNumberToFormName[String(stepNumber)]}' form.`);
      }
    }

    const { success, data, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      result.data,
      stepNumber
    );

    if (!success) {
      return apiResponse(500, saveError || `Failed to save ${stepNumberToFormName[String(stepNumber)]} data`);
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const responseData: any = {
      ivfScores: data,
      isGuest: !context!.isAuthenticated
    };

    // Add step-specific response data
    if (stepNumber < 3) {
      responseData.nextStep = stepNumber + 1;
    } else {
      responseData.allStepsCompleted = true;
      responseData.nextAction = !context!.isAuthenticated ? "email_verification" : "view_results";
    }

    const response = apiResponse(200, `${stepNumberToFormName[String(stepNumber)]} data saved successfully`, responseData);
    
    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("POST form error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-scores/form
 * Update IVF form data for authenticated users or guests
 * Requires category parameter to determine which step to update
 */
export async function PUT(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const { searchParams } = new URL(req.url);
    const category = searchParams.get('category'); // 'biological', 'lifestyle', 'environmental'

    if (!category || !['biological', 'lifestyle', 'environmental'].includes(category)) {
      return apiResponse(400, "Category parameter is required and must be 'biological', 'lifestyle', or 'environmental'");
    }

    const body = await req.json();
    let result;

    // Validate data based on category
    switch (category) {
      case 'biological':
        result = updateBiologicalFactorsSchema.safeParse(body);
        break;
      case 'lifestyle':
        result = updateLifestylePsychosocialSchema.safeParse(body);
        break;
      case 'environmental':
        result = updateEnvironmentalSocioeconomicSchema.safeParse(body);
        break;
      default:
        return apiResponse(400, "Invalid category");
    }

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Get existing data to merge with updates
    const { success: getSuccess, data: existingData } = await getIVFData(context!);

    if (!getSuccess || !existingData) {
      return apiResponse(404, "IVF scores not found. Use POST to create.");
    }

    // Merge existing data with updates
    const mergedData = { ...existingData, ...result.data };

    const { success, data, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      mergedData,
      existingData.current_step || 1
    );

    if (!success) {
      return apiResponse(500, saveError || `Failed to update ${category} data`);
    }

    const response = apiResponse(200, `${category} data updated successfully`, {
      ivfScores: data,
      isGuest: !context!.isAuthenticated
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("PUT form error:", error);
    return apiResponse(500, "Internal server error");
  }
}