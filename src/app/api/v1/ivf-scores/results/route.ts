import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";
import { getGuestSessionToken, getGuestSession, convertGuestToUser } from "@/utils/api/guestSession";
import { z } from "zod";
import { calculateDynamicIVFScore } from "@/lib/services/dynamic-scoring.service";

const prisma = new PrismaClient();

const guestResultsSchema = z.object({
  guestSessionToken: z.string().min(1, "Guest session token is required"),
  email: z.string().email("Valid email is required"),
});

/**
 * GET /api/v1/ivf-scores/results
 * Get IVF score results - requires authentication OR verified guest session
 */
export async function GET(req: NextRequest) {
  try {
    // First try to authenticate the user
    const authResult = await authenticate(req);
    
    if (!authResult.error) {
      // User is authenticated - get their IVF scores
      const ivfScores = await prisma.ivf_scores.findUnique({
        where: { user_id: authResult.user.id },
      });

      if (!ivfScores) {
        return apiResponse(404, "IVF scores not found for this user");
      }

      if (ivfScores.current_step < 3) {
        return apiResponse(400, "All three steps must be completed to view results");
      }

      // Calculate IVF score using dynamic scoring system
      const score = await calculateDynamicIVFScore(ivfScores);

      return apiResponse(200, undefined, {
        ivfScores,
        calculatedScore: score,
        isAuthenticated: true,
        completedAt: ivfScores.updated_at
      });
    }

    // User is not authenticated - check for verified guest session
    const url = new URL(req.url);
    const guestSessionToken = url.searchParams.get("guestSessionToken") || getGuestSessionToken(req);
    const email = url.searchParams.get("email");

    if (!guestSessionToken || !email) {
      return apiResponse(401, "Authentication required or provide verified guest session token and email");
    }

    const guestSession = await getGuestSession(guestSessionToken);
        
    if (!guestSession) {
      return apiResponse(404, "Guest session not found or expired");
    }

    if (guestSession.email !== email) {
      return apiResponse(400, "Email does not match guest session");
    }

    if (!guestSession.is_verified) {
      return apiResponse(403, "Email verification required to view results");
    }

    if (guestSession.current_step < 3) {
      return apiResponse(400, "All three steps must be completed to view results");
    }

    // Calculate IVF score for guest using dynamic scoring system
    const score = await calculateDynamicIVFScore(guestSession.ivf_data);

    return apiResponse(200, undefined, {
      ivfScores: {
        ...guestSession.ivf_data,
        current_step: guestSession.current_step,
        id: guestSession.id,
        created_at: guestSession.created_at,
        updated_at: guestSession.updated_at,
      },
      calculatedScore: score,
      isAuthenticated: false,
      isGuest: true,
      completedAt: guestSession.updated_at,
      message: "Results available for verified guest session"
    });
  } catch (error) {
    console.error("GET results error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-scores/results
 * Convert verified guest session to authenticated user account
 */
export async function POST(req: NextRequest) {
  try {
    // This endpoint requires authentication to create the user account
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const result = guestResultsSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    const { guestSessionToken } = result.data;

    // Verify guest session and convert to user data
    const success = await convertGuestToUser(guestSessionToken, user.id);

    if (!success) {
      return apiResponse(400, "Failed to convert guest session. Session may not exist, be unverified, or email may not match.");
    }

    // Get the newly created IVF scores
    const ivfScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (!ivfScores) {
      return apiResponse(500, "Failed to retrieve converted IVF scores");
    }

    const score = await calculateDynamicIVFScore(ivfScores);

    return apiResponse(200, "Guest data successfully converted to user account", {
      ivfScores,
      calculatedScore: score,
      isAuthenticated: true,
      convertedFromGuest: true,
      completedAt: ivfScores.updated_at
    });
  } catch (error) {
    console.error("POST results conversion error:", error);
    return apiResponse(500, "Internal server error");
  }
}


