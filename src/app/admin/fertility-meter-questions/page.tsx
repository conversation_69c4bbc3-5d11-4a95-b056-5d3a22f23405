"use client";

import { useState, useEffect } from "react";
import { FormWithQuestions, FormCategory, FormBuilderQuestion } from "@/types/fertility-questions";
import {
  getFormsByCategory,
  initializeDefaultForms,
  addQuestion,
  updateQuestion,
  deleteQuestion,
  reorderQuestions
} from "@/lib/services/fertility-questions.service";
import { FormBuilder } from "@/components/admin/fertility-questions/FormBuilder";
import { Card, CardContent } from "@/components/ShadcnUI/card";
import { Button } from "@/components/ShadcnUI/button";
import { RefreshCw, AlertCircle } from "lucide-react";
import { toast } from "sonner";

export default function FertilityMeterQuestionsPage() {
  const [forms, setForms] = useState<Record<FormCategory, FormWithQuestions | null>>({
    biological: null,
    lifestyle: null,
    environmental: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load forms on component mount
  useEffect(() => {
    loadForms();
  }, []);

  const loadForms = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Initialize default forms if they don't exist
      await initializeDefaultForms();

      // Load forms by category
      const formsByCategory = await getFormsByCategory();
      setForms(formsByCategory);
    } catch (err) {
      console.error("Error loading forms:", err);
      setError("Failed to load forms. Please try again.");
      toast.error("Failed to load forms");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveQuestion = async (formId: string, question: FormBuilderQuestion) => {
    try {
      await addQuestion(formId, question);
      await loadForms(); // Refresh forms after adding
    } catch (error) {
      console.error("Error saving question:", error);
      throw error;
    }
  };

  const handleUpdateQuestion = async (questionId: string, questionData: Partial<FormBuilderQuestion>) => {
    try {
      await updateQuestion(questionId, questionData);
      await loadForms(); // Refresh forms after updating
    } catch (error) {
      console.error("Error updating question:", error);
      throw error;
    }
  };

  const handleDeleteQuestion = async (questionId: string) => {
    try {
      await deleteQuestion(questionId);
      await loadForms(); // Refresh forms after deleting
    } catch (error) {
      console.error("Error deleting question:", error);
      throw error;
    }
  };

  const handleReorderQuestions = async (formId: string, questionOrders: { id: string; order: number }[]) => {
    try {
      await reorderQuestions(formId, questionOrders);
      await loadForms(); // Refresh forms after reordering
    } catch (error) {
      console.error("Error reordering questions:", error);
      throw error;
    }
  };

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-lg font-semibold mb-2">Error Loading Forms</h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={loadForms} className="gap-2">
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <FormBuilder
        forms={forms}
        onSaveQuestion={handleSaveQuestion}
        onUpdateQuestion={handleUpdateQuestion}
        onDeleteQuestion={handleDeleteQuestion}
        onReorderQuestions={handleReorderQuestions}
        onRefresh={loadForms}
        isLoading={isLoading}
      />
    </div>
  );
}