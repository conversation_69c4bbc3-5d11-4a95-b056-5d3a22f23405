import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { But<PERSON> } from "@/components/ShadcnUI/button";
import { Badge } from "@/components/ShadcnUI/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import {
  Users,
  UserPlus,
  Edit3,
  Trash2,
  ChevronLeft,
  ChevronRight,
  Search,
  Filter,
  MoreHorizontal,
  Mail,
  Calendar,
  Shield,
  User
} from "lucide-react";
import {
  deleteUser,
  getUsers,
  type UsersPageProps,
} from "@/lib/services/user-management.service";
import {
  getUserDisplayName,
  getProviderBadge,
  formatDate,
  getVerifiedUsersCount,
  getActiveTodayUsersCount,
  getAdminUsersCount,
  PER_PAGE,
} from "@/lib/utils/user-management.utils";

export default async function UsersPage({ searchParams }: UsersPageProps) {
  const resolvedSearchParams = await searchParams;
  const page = parseInt(resolvedSearchParams?.page || '1');
  const search = resolvedSearchParams?.search || '';
  const perPage = parseInt(resolvedSearchParams?.per_page || PER_PAGE.toString());
  
  const { users, total, error } = await getUsers(page, search, perPage);
  
  const totalPages = Math.ceil(total / perPage);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Users className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">User Management</h1>
            <p className="text-muted-foreground">
              Manage user accounts and permissions
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm" className="gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button size="sm" className="gap-2">
            <UserPlus className="h-4 w-4" />
            Add User
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Total Users</span>
            </div>
            <div className="text-2xl font-bold">{total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Verified</span>
            </div>
            <div className="text-2xl font-bold">
              {getVerifiedUsersCount(users)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Active Today</span>
            </div>
            <div className="text-2xl font-bold">
              {getActiveTodayUsersCount(users)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Admins</span>
            </div>
            <div className="text-2xl font-bold">
              {getAdminUsersCount(users)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Users List
              </CardTitle>
              <CardDescription>
                {search ? `Showing results for "${search}"` : `Showing ${users.length} of ${total} users`}
              </CardDescription>
            </div>
            
            {/* Search */}
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <form method="get" className="flex gap-2">
                  <input
                    type="text"
                    name="search"
                    placeholder="Search users..."
                    defaultValue={search}
                    className="pl-10 pr-4 py-2 border rounded-md text-sm w-64"
                  />
                  <input type="hidden" name="page" value="1" />
                  <Button type="submit" size="sm" variant="outline">
                    Search
                  </Button>
                </form>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-8">
              <div className="text-red-600 mb-2">Error loading users</div>
              <div className="text-sm text-muted-foreground">{error}</div>
            </div>
          ) : (
            <>
              {users.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No users found</h3>
                  <p className="text-muted-foreground mb-4">
                    {search ? 'Try adjusting your search criteria' : 'Get started by adding your first user'}
                  </p>
                  <Button className="gap-2">
                    <UserPlus className="h-4 w-4" />
                    Add User
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Table */}
                  <div className="rounded-lg border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[300px]">User</TableHead>
                          <TableHead>Provider</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead>Last Sign In</TableHead>
                          <TableHead className="w-[100px]">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {users.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                  <User className="h-4 w-4 text-primary" />
                                </div>
                                <div>
                                  <div className="font-medium">{getUserDisplayName(user)}</div>
                                  <div className="text-sm text-muted-foreground">{user.email}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge 
                                variant="secondary" 
                                className={getProviderBadge(user)}
                              >
                                {user.app_metadata?.provider || 'email'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <div className={`w-2 h-2 rounded-full ${
                                  user.email_confirmed_at ? 'bg-green-500' : 'bg-yellow-500'
                                }`} />
                                <span className="text-sm">
                                  {user.email_confirmed_at ? 'Verified' : 'Pending'}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {formatDate(user.created_at)}
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {formatDate(user.last_sign_in_at)}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1">
                                <Button size="sm" variant="ghost" className="h-8 w-8 p-0" title="Edit User">
                                  <Edit3 className="h-3 w-3" />
                                </Button>
                                <form action={deleteUser} style={{ display: 'inline' }}>
                                  <input type="hidden" name="userId" value={user.id} />
                                  <Button 
                                    type="submit" 
                                    size="sm" 
                                    variant="ghost" 
                                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                    title="Delete User"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </form>
                                <Button size="sm" variant="ghost" className="h-8 w-8 p-0" title="More Options">
                                  <MoreHorizontal className="h-3 w-3" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">
                        Showing {((page - 1) * perPage) + 1} to {Math.min(page * perPage, total)} of {total} users
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={page <= 1}
                          asChild
                        >
                          <a href={`?page=${page - 1}${search ? `&search=${encodeURIComponent(search)}` : ''}`}>
                            <ChevronLeft className="h-4 w-4" />
                            Previous
                          </a>
                        </Button>
                        
                        <div className="flex items-center gap-1">
                          {[...Array(Math.min(totalPages, 5))].map((_, i) => {
                            const pageNum = i + 1;
                            return (
                              <Button
                                key={pageNum}
                                variant={page === pageNum ? "default" : "outline"}
                                size="sm"
                                asChild
                              >
                                <a href={`?page=${pageNum}${search ? `&search=${encodeURIComponent(search)}` : ''}`}>
                                  {pageNum}
                                </a>
                              </Button>
                            );
                          })}
                        </div>

                        <Button
                          variant="outline"
                          size="sm"
                          disabled={page >= totalPages}
                          asChild
                        >
                          <a href={`?page=${page + 1}${search ? `&search=${encodeURIComponent(search)}` : ''}`}>
                            Next
                            <ChevronRight className="h-4 w-4" />
                          </a>
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
