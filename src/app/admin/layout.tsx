import React from "react";
import <PERSON> from "next/link";
import { <PERSON><PERSON> } from "@/components/ShadcnUI/button";
import {
  Shield,
  Users,
  Settings,
  Home,
  ChevronRight,
  Heart
} from "lucide-react";
import AdminAuthWrapper from "@/components/AdminAuthWrapper";

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const navigationItems = [
    {
      title: "Dashboard",
      href: "/admin",
      icon: Home,
      description: "Overview and analytics"
    },
    {
      title: "Roles & Permissions",
      href: "/admin/roles-permissions",
      icon: Shield,
      description: "Manage user roles and permissions"
    },
    {
      title: "Users",
      href: "/admin/users",
      icon: Users,
      description: "User management and accounts"
    },
    // {
    //   title: "Analytics",
    //   href: "/admin/analytics",
    //   icon: BarChart3,
    //   description: "System analytics and reports"
    // },
    {
      title: "Fertility Questions",
      href: "/admin/fertility-meter-questions",
      icon: Heart,
      description: "Manage fertility assessment questions"
    },
    {
      title: "Settings",
      href: "/admin/settings",
      icon: Settings,
      description: "System configuration"
    },
  ];

  return (
    <AdminAuthWrapper>
      <div className="min-h-screen bg-background">
        {/* Header - Full Width */}
        <header className="fixed top-0 left-0 right-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Link href="/" className="flex items-center gap-2">
                  <Shield className="h-6 w-6 text-primary" />
                  <span className="font-bold text-xl">GIVF Admin</span>
                </Link>
                <div className="hidden md:flex items-center gap-2 text-sm text-muted-foreground">
                  <ChevronRight className="h-4 w-4" />
                  <span>Administration Panel</span>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/dashboard">
                    <Home className="h-4 w-4 mr-2" />
                    Back to App
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Fixed Sidebar */}
        <aside className="fixed left-0 top-16 bottom-0 w-64 bg-background border-r overflow-y-auto z-40">
          <div className="p-6">
            <nav className="space-y-3">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="flex items-start gap-3 rounded-lg px-4 py-3 text-sm transition-colors hover:bg-accent hover:text-accent-foreground group"
                  >
                    <Icon className="h-5 w-5 mt-0.5 flex-shrink-0 text-muted-foreground group-hover:text-accent-foreground" />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-foreground group-hover:text-accent-foreground">
                        {item.title}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1 leading-relaxed">
                        {item.description}
                      </div>
                    </div>
                  </Link>
                );
              })}
            </nav>
          </div>
        </aside>

        {/* Main Content - Full Width with Left Margin */}
        <main className="ml-64 pt-16">
          <div className="p-8">
            {children}
          </div>
        </main>
      </div>
    </AdminAuthWrapper>
  );
}
