import React from "react";
import <PERSON> from "next/link";
import { <PERSON><PERSON> } from "@/components/ShadcnUI/button";
import {
  Shield,
  Users,
  Settings,
  Home,
  ChevronRight,
  Heart
} from "lucide-react";
import AdminAuthWrapper from "@/components/AdminAuthWrapper";
import "../../styles/admin-theme.css";
import "../../styles/admin-overrides.css";

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const navigationItems = [
    {
      title: "Dashboard",
      href: "/admin",
      icon: Home,
      description: "Overview and analytics"
    },
    {
      title: "Roles & Permissions",
      href: "/admin/roles-permissions",
      icon: Shield,
      description: "Manage user roles and permissions"
    },
    {
      title: "Users",
      href: "/admin/users",
      icon: Users,
      description: "User management and accounts"
    },
    // {
    //   title: "Analytics",
    //   href: "/admin/analytics",
    //   icon: BarChart3,
    //   description: "System analytics and reports"
    // },
    {
      title: "Fertility Questions",
      href: "/admin/fertility-meter-questions",
      icon: Heart,
      description: "Manage fertility assessment questions"
    },
    {
      title: "Settings",
      href: "/admin/settings",
      icon: Settings,
      description: "System configuration"
    },
  ];

  return (
    <AdminAuthWrapper>
      <div className="admin-layout min-h-screen">
        {/* Header - Full Width */}
        <header className="admin-header fixed top-0 left-0 right-0 z-50">
          <div className="admin-header-content">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Link href="/" className="admin-logo">
                  <Shield className="h-6 w-6" />
                  <span>GIVF Admin</span>
                </Link>
                <div className="hidden md:flex items-center gap-2 admin-breadcrumb">
                  <ChevronRight className="h-4 w-4" />
                  <span>Administration Panel</span>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" asChild className="admin-btn admin-btn-outline">
                  <Link href="/dashboard">
                    <Home className="h-4 w-4 mr-2" />
                    Back to App
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Fixed Sidebar */}
        <aside className="admin-sidebar fixed left-0 top-16 bottom-0 w-64 overflow-y-auto z-40">
          <div className="admin-sidebar-content">
            <nav className="space-y-3">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="admin-nav-item admin-slide-in"
                  >
                    <Icon className="admin-nav-icon" />
                    <div className="flex-1 min-w-0">
                      <div className="admin-nav-title">
                        {item.title}
                      </div>
                      <div className="admin-nav-description">
                        {item.description}
                      </div>
                    </div>
                  </Link>
                );
              })}
            </nav>
          </div>
        </aside>

        {/* Main Content - Full Width with Left Margin */}
        <main className="admin-main-content">
          <div className="admin-page-content admin-animate-in">
            {children}
          </div>
        </main>
      </div>
    </AdminAuthWrapper>
  );
}
