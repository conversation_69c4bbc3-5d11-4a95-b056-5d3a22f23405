"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import ForgotPasswordWithOTP from "@/components/ForgotPasswordPage/ForgotPasswordPage";
import { createClient } from "@/utils/supabase/client";
import { PasswordResetStep } from "@/components/ForgotPasswordPage";

const ResetPasswordPageContainer = () => {
  const router = useRouter();
  const supabase = createClient();

  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState<PasswordResetStep>(
    PasswordResetStep.PASSWORD_RESET
  );
  const [email, setEmail] = useState("");

  useEffect(() => {
    const checkUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        router.push("/login");
      } else {
        setEmail(user.email ?? "");
      }
    };
    checkUser();
  }, [supabase, router]);

  const handleResetPassword = async (email: string, newPassword: string) => {
    setIsLoading(true);
    setError(null);

    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) {
      setError(error.message);
    } else {
      setCurrentStep(PasswordResetStep.SUCCESS);
      await supabase.auth.signOut();
    }
    setIsLoading(false);
  };

  return (
    <ForgotPasswordWithOTP
      currentStep={currentStep}
      email={email}
      isLoading={isLoading}
      errorMessage={error ?? undefined}
      onResetPassword={handleResetPassword}
      onBackToLogin={() => router.push("/login")}
    />
  );
};

export default ResetPasswordPageContainer;