import type { forms, fertility_questions, form_options } from "@/generated/prisma";
import { FormFieldType } from "@/generated/prisma";

// Base types from Prisma
export type Form = forms;
export type FertilityQuestion = fertility_questions;
export type FormOption = form_options;
export { FormFieldType };

// Extended types for UI
export interface FormWithQuestions extends Form {
  questions: QuestionWithOptions[];
}

export interface QuestionWithOptions extends FertilityQuestion {
  options: FormOption[];
}

// Form builder types
export interface FormBuilderQuestion {
  id?: string;
  question_text: string;
  field_type: FormFieldType;
  placeholder?: string;
  min_value?: number;
  max_value?: number;
  step?: number;
  unit?: string;
  order: number;
  options?: FormBuilderOption[];
}

export interface FormBuilderOption {
  id?: string;
  option_text: string;
  value?: string;
  order: number;
}

export interface FormBuilderForm {
  id?: string;
  name: string;
  description?: string;
  questions: FormBuilderQuestion[];
}

// Form categories for the three forms
export type FormCategory = 'biological' | 'lifestyle' | 'environmental';

export interface FormCategoryConfig {
  id: FormCategory;
  name: string;
  description: string;
  icon: string;
}

// Field validation rules
export interface FieldValidation {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: string;
  message?: string;
}

// Form field configuration for the builder
export interface FieldConfig {
  type: FormFieldType;
  label: string;
  description: string;
  supportsOptions: boolean;
  supportsRange: boolean;
  supportsPlaceholder: boolean;
  supportsUnit: boolean;
}

// Form submission data
export interface FormSubmissionData {
  [questionId: string]: string | number | boolean;
}

// API response types
export interface FormsApiResponse {
  forms: FormWithQuestions[];
}

export interface FormApiResponse {
  form: FormWithQuestions;
}

export interface QuestionApiResponse {
  question: QuestionWithOptions;
}

// Form builder state
export interface FormBuilderState {
  selectedForm: FormCategory | null;
  forms: Record<FormCategory, FormBuilderForm>;
  isLoading: boolean;
  error: string | null;
  isDirty: boolean;
}

// Drag and drop types
export interface DragItem {
  id: string;
  type: 'question';
  index: number;
}

export interface DropResult {
  dragIndex: number;
  hoverIndex: number;
}
