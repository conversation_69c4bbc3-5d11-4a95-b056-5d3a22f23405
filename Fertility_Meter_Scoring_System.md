**Instruction for AI: Integrating Scoring System with Form Builder for IVF Fertility Meter**

**Objective:**  
Enable the form builder to dynamically assign and manage scores for each question in the Physiological, Psychosocial & Lifestyle, and Environmental & Socioeconomic forms, using a simplified and unified scoring system. This will allow admins to easily configure how each answer affects the overall IVF Fertility Meter score.

### 1. Minimize and Standardize Scoring Types

To keep the system user-friendly for admins, use only **two scoring types**:

| Scoring Type          | Description                                                        | Example Use Cases                               |
|-----------------------|--------------------------------------------------------------------|-------------------------------------------------|
| **Range**             | Numeric input mapped to score buckets                              | Age, BMI, AMH, AFC, Income, Duration questions  |
| **Single Choice**     | Predefined options, each mapped to a score (positive or negative)  | Yes/No, Frequency, Support, Stress, Lifestyle   |

- *Composite* scores (e.g., average of AMH and AFC) are handled internally and not exposed as a separate type for admin configuration.

### 2. Schema for Each Question

For every question, require this following columns:

- `scoring_type`: `"range"` or `"single_choice"`
- `scoring_config`:  
  - For `"range"`: List of `{min, max, score}` objects.  
  - For `"single_choice"`: List of `{option, score}` objects.

**Examples:**

**Range:**
```json
{
  "question_text": "What is your age?",
  "scoring_type": "range",
  "scoring_config": [
    { "min": 0, "max": 34, "score": 1.0 },
    { "min": 35, "max": 37, "score": 0.9 },
    { "min": 38, "max": 40, "score": 0.7 },
    { "min": 41, "max": 42, "score": 0.5 },
    { "min": 43, "max": 120, "score": 0.3 }
  ]
}
```

**Single Choice:**
```json
{
  "question_text": "Do you have previous kids in the past 5 years?",
  "scoring_type": "single_choice",
  "scoring_config": [
    { "option": "Yes", "score": 1 },
    { "option": "No", "score": 0 }
  ]
}
```

### 3. Form Builder Integration Steps

**A. For Each Question:**
- Admin selects the scoring type (`range` or `single_choice`).
- Admin enters the scoring configuration (ranges or options with scores).
- The system stores the scoring configuration alongside the question metadata.

**B. On Form Submission:**
- For each answer, the system looks up the question’s scoring type and config.
- The answer is mapped to its corresponding score using the config.
- Scores are saved for specific user.

**C. Final Score Calculation:**
- Calculate sub-scores for each category (average or weighted as per the IVF Fertility Meter rules).
- Combine sub-scores using the formula:
  $$
  \text{IVF-SPSNew} = 100 \times (0.5 \times \text{Pnew} + 0.25 \times \text{Psynew} + 0.25 \times \text{Enew})
  $$
- If the result is below 60, set to 60.

### 4. Admin User Experience

- **Simple:** Only two scoring types to choose from.
- **Flexible:** Admin can define as many ranges or options as needed.
- **Consistent:** All forms and questions follow the same scoring logic, making it easy to maintain and explain.

### 5. Task Summary

- For each question in the form builder, prompt the admin to select a scoring type (`range` or `single_choice`).
- Collect the scoring configuration for that type.
- Store all scoring data with the question.
- On form submission, use the scoring config to compute the user's score for each answer.
- Aggregate scores by category, apply the IVF Fertility Meter formula, and output the final result as specified in the calculation PDF[1].

**This approach ensures the system is powerful yet easy for admins to use, with minimal confusion and maximum flexibility.**

[1] https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/attachments/6298778/0c2fd683-e9ea-4c6e-9d24-184fa956df22/IVF_Fertility_Meter_Calculation.pdf